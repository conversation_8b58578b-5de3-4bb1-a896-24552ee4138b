import SwiftUI
import CommonCrypto
import AVFoundation
import Translation

// 添加 String 扩展用于生成 MD5
extension String {
    var md5: String {
        let data = Data(self.utf8)
        var digest = [UInt8](repeating: 0, count: Int(CC_MD5_DIGEST_LENGTH))
        _ = data.withUnsafeBytes { buffer in
            CC_MD5(buffer.baseAddress, CC_LONG(data.count), &digest)
        }
        return digest.map { String(format: "%02hhx", $0) }.joined()
    }
}

// 自定义悬停按钮样式 - 带文字标签
struct HoverButtonStyle: ButtonStyle {
    let label: String
    @State private var isHovering = false

    func makeBody(configuration: Configuration) -> some View {
        VStack(spacing: 2) {
            // 主按钮区域
            configuration.label
                .frame(width: 32, height: 32)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(Color.white.opacity(
                            configuration.isPressed ? 0.4 : (isHovering ? 0.2 : 0.0)
                        ))
                        .animation(.easeInOut(duration: 0.15), value: configuration.isPressed)
                        .animation(.easeInOut(duration: 0.15), value: isHovering)
                )
                .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
                .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)

            // 悬停时显示文字标签 - 在按钮下方
            if isHovering {
                Text(label)
                    .font(.system(size: 9, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, 4)
                    .padding(.vertical, 2)
                    .background(
                        RoundedRectangle(cornerRadius: 3)
                            .fill(Color.black.opacity(0.8))
                    )
                    .transition(.opacity.combined(with: .scale(scale: 0.8)))
                    .fixedSize() // 确保文字不被截断
            }
        }
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovering = hovering
            }
        }
    }
}

enum SRTExportType {
    case bilingual
    case original
    case translation
}

struct AITranslationView: View {
    let originalSegments: [EditableSegment]
    @Binding var optimizedSegments: [EditableSegment]
    @State private var isProcessing = false
    @State private var currentStep = ProcessingStep.none
    @State private var progress = ""
    @State private var aiTargetLanguage: String = "zh-Hans"
    @State private var manualKeywords: String = ""
    @State private var showAdvancedOptions = false

    // AI校对建议导航状态
    @State private var currentSuggestionIndex: Int = 0
    @State private var totalSuggestions: Int = 0
    @State private var showSuggestionNavigation: Bool = false

    let transcriptionLanguage: String
    @State private var totalBatches: Int = 0
    @State private var completedBatches: Int = 0
    
    // 翻译配置
    @State private var configuration: TranslationSession.Configuration?
    
    // 添加跟随滚动和翻译控制状态
    @State private var enableTranslation: Bool = false
    @State private var enableAutoScroll: Bool = false
    @State private var currentTime: Double = 0.0
    @Binding var player: AVPlayer?  // 保持这个绑定，但我们会通过 PlayerManager 来管理
    @State private var timer: Timer?
    @State private var duration: Double = 0.0  // 添加duration状态

    // 修改状态变量
    @State private var selectedSegmentId: UUID?  // 替代 selectedSegmentIndex
    @Binding var subtitleMode: SubtitleMode
    
    @State private var scrollProxy: ScrollViewProxy? = nil // 用于获取 ScrollView 的代理
    @State private var lastScrolledIndex: Int = -1  // 添加这一行
    @State private var cursorPosition: Int? = nil

    @State private var apiErrorMessage: String? = nil
    
    // 添加转录相关状态
    @Binding var isTranscribing: Bool // 控制转录进度指示器的显示

    let onReTranscribe: (EditableSegment) -> Void
    let onExportVideoClip: (String, String) -> Void // 导出视频片段回调

    // 视频片段选择器状态
    @State private var clipStartTime: String = "00:00:00"
    @State private var clipEndTime: String = "00:00:00"
    
    // 用于跟踪处理步骤
    enum ProcessingStep {
        case none
        case segmenting
        case correcting
        case translating
    }
    
    // 新增变量用于标记需要注意的字幕行
    @State private var shortSegmentIndices: Set<Int> = []
    
    // 初始化 数据流：editableSegments -> originalSegments -> 处理 -> 更新 -> optimizedSegments
    init(
        originalSegments: [EditableSegment],
        optimizedSegments: Binding<[EditableSegment]>,
        transcriptionLanguage: String,
        player: Binding<AVPlayer?>,
        subtitleMode: Binding<SubtitleMode>,
        isTranscribing: Binding<Bool>, // 修改为 Binding<Bool>
        onReTranscribe: @escaping (EditableSegment) -> Void, // <-- add this
        onExportVideoClip: @escaping (String, String) -> Void // 导出视频片段回调
        ){
        self.originalSegments = originalSegments // 原始段落，不可变let
        self._optimizedSegments = optimizedSegments // 可编辑的工作副本，可重置 
        
        // 如果优化段落为空，则初始化为原始段落的拷贝
        if optimizedSegments.wrappedValue.isEmpty && !originalSegments.isEmpty {
            DispatchQueue.main.async {
                optimizedSegments.wrappedValue = originalSegments
            }
        } 
        
        self.transcriptionLanguage = transcriptionLanguage
        self._player = player
        self._subtitleMode = subtitleMode // 关键：初始化 Binding
        self._isTranscribing = isTranscribing  // 直接使用绑定
        self.onReTranscribe = onReTranscribe // <-- add this
        self.onExportVideoClip = onExportVideoClip // 导出视频片段回调
    }
    
    var body: some View {
        ZStack {
            // 主界面内容
            VStack {
            // 添加播放器状态指示
            if player == nil {
                Text("等待播放器初始化...")
                    .foregroundColor(.gray)
            }
            
            // 🎨 紧凑型图标工具栏 - 参考图片设计
            HStack(spacing: 0) {
                Spacer()

                // 工具栏图标组
                HStack(spacing: 6) {
                    // AI校对图标 - 点击显示菜单
                    Menu {
                        Button(action: {
                            checkProFeatureAndRun {
                                Task { await processProofreading() }
                            }
                        }) {
                            Label("AI校对", systemImage: "wand.and.stars")
                        }
                        .disabled(isProcessing)

                        Divider()

                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                showAdvancedOptions.toggle()
                            }
                        }) {
                            Label("添加关键词", systemImage: "plus.circle")
                        }
                    } label: {
                        Image(systemName: "wand.and.stars")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(isProcessing ? .secondary : .primary)
                    }
                    .buttonStyle(HoverButtonStyle(label: "AI校对"))

                    // AI断句图标
                    Button(action: {
                        checkProFeatureAndRun {
                            Task { await processSegmentation() }
                        }
                    }) {
                        Image(systemName: "waveform")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(isProcessing ? .secondary : .primary)
                    }
                    .buttonStyle(HoverButtonStyle(label: "AI断句"))
                    .disabled(isProcessing)

                    // 翻译图标 - 支持AI翻译和Apple翻译选择
                    Menu {
                        Button(action: {
                            checkProFeatureAndRun {
                                Task { await processTranslation() }
                            }
                        }) {
                            Label("AI翻译", systemImage: "sparkles")
                        }
                        .disabled(isProcessing)

                        Divider()

                        Button(action: {
                            enableTranslation.toggle()
                            if enableTranslation {
                                updateTranslationConfiguration()
                            }
                        }) {
                            Label(enableTranslation ? "关闭Apple翻译" : "开启Apple翻译",
                                  systemImage: enableTranslation ? "globe.americas.fill" : "globe.americas")
                        }

                        Divider()

                        // 语言选择子菜单
                        Menu("目标语言") {
                            Button("🇺🇸 英语") {
                                aiTargetLanguage = "en-US"
                                if enableTranslation { updateTranslationConfiguration() }
                            }
                            Button("🇨🇳 中文") {
                                aiTargetLanguage = "zh-Hans"
                                if enableTranslation { updateTranslationConfiguration() }
                            }
                            Button("🇯🇵 日语") {
                                aiTargetLanguage = "ja-JP"
                                if enableTranslation { updateTranslationConfiguration() }
                            }
                            Button("🇰🇷 韩语") {
                                aiTargetLanguage = "ko-KR"
                                if enableTranslation { updateTranslationConfiguration() }
                            }
                            Button("🇫🇷 法语") {
                                aiTargetLanguage = "fr-FR"
                                if enableTranslation { updateTranslationConfiguration() }
                            }
                            Button("🇩🇪 德语") {
                                aiTargetLanguage = "de-DE"
                                if enableTranslation { updateTranslationConfiguration() }
                            }
                            Button("🇪🇸 西班牙语") {
                                aiTargetLanguage = "es-ES"
                                if enableTranslation { updateTranslationConfiguration() }
                            }
                            Button("🇮🇹 意大利语") {
                                aiTargetLanguage = "it-IT"
                                if enableTranslation { updateTranslationConfiguration() }
                            }
                            Button("🇹🇭 泰语") {
                                aiTargetLanguage = "th-TH"
                                if enableTranslation { updateTranslationConfiguration() }
                            }
                        }
                    } label: {
                        Image(systemName: "globe")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.primary)
                    }
                    .buttonStyle(HoverButtonStyle(label: "翻译"))

                    // 跟随滚动图标
                    Button(action: {
                        enableAutoScroll.toggle()
                        setupTimer()
                    }) {
                        Image(systemName: enableAutoScroll ? "arrow.up.to.line.compact" : "arrow.up.to.line")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(enableAutoScroll ? .blue : .secondary)
                    }
                    .buttonStyle(HoverButtonStyle(label: "跟随滚动"))

                    // 导出按钮
                    Menu {
                        Button(action: { exportSRT(type: .bilingual) }) {
                            Label("导出双语字幕", systemImage: "doc.text.below.ecg")
                        }
                        Button(action: { exportSRT(type: .original) }) {
                            Label("导出原文字幕", systemImage: "doc.text")
                        }
                        Button(action: { exportSRT(type: .translation) }) {
                            Label("导出翻译字幕", systemImage: "doc.text.magnifyingglass")
                        }
                    } label: {
                        Image(systemName: "square.and.arrow.up")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.primary)
                    }
                    .buttonStyle(HoverButtonStyle(label: "导出"))
                    .disabled(optimizedSegments.isEmpty)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color(NSColor.controlBackgroundColor).opacity(0.8))
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color(NSColor.separatorColor).opacity(0.3), lineWidth: 1)
                )
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)

            // 关键词输入区域（可折叠）
            if showAdvancedOptions {
                HStack {
                    TextField("添加主题关键词，提升AI处理准确性", text: $manualKeywords)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .onChange(of: manualKeywords) { newValue in
                            if newValue.count > 1000 {
                                manualKeywords = String(newValue.prefix(1000))
                            }
                        }

                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            showAdvancedOptions = false
                        }
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 16))
                            .foregroundColor(.secondary)
                    }
                    .buttonStyle(.plain)
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 8)
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
            
            // 处理进度显示
            if !isTranscribing && isProcessing {
                VStack {
                    ProgressView(progress)
                        .padding()
                    Text(currentStepDescription)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }else if isTranscribing {
                VStack {
                    ProgressView("正在转录音频...")
                        .padding()
                    Text("转录时间可能较长，请耐心等待...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // // 字幕列表（与 selectedSegment == 1 页面相同的展示方式）
            ScrollViewReader { proxy in
                List {
                   ForEach(optimizedSegments.indices, id: \.self) { index in
                        createSegmentView(for: optimizedSegments[index], at: index)
                   }
                }
               .listStyle(PlainListStyle()) // 去除列表样式，使体验更干净
               .onChange(of: currentTime) { newTime in
                   // 只在启用自动滚动时执行滚动
                   if enableAutoScroll, let currentIndex = getCurrentSegmentIndex() {
                       // 使用动画使滚动更平滑，并且只有当需要显示的段落不在可视区域内时才滚动
                       if currentIndex != lastScrolledIndex {
                           lastScrolledIndex = currentIndex
                           withAnimation(.easeInOut(duration: 0.3)) {
                               // 明确使用 UnitPoint.center 作为锚点，确保字幕行居中
                               proxy.scrollTo(currentIndex, anchor: UnitPoint.center)
                           }
                       }
                   }
               }
                .onAppear {
                    // 设置滚动代理供播放器时间变化时使用
                    self.scrollProxy = proxy
                }
                .onDisappear {
                    self.scrollProxy = nil
                }
                .translationTask(configuration) { session in
                    // 只有在启用翻译时才处理
                    if enableTranslation {
                        // 创建翻译请求
                        let requests = optimizedSegments.map { TranslationSession.Request(sourceText: $0.text, clientIdentifier: $0.id.uuidString) }
                        
                        // 分批处理翻译请求，每次处理5行
                        let batchSize = 5
                        var completedCount = 0
                        
                        // 分批翻译
                        for i in stride(from: 0, to: requests.count, by: batchSize) {
                            let end = min(i + batchSize, requests.count)
                            let batchRequests = Array(requests[i..<end])
                            
                            if let responses = try? await session.translations(from: batchRequests) {
                                // 更新这5行的翻译结果
                                for response in responses {
                                    updateTranslation(response: response)
                                    completedCount += 1
                                    
                                    // 更新进度
                                    let progress = Double(completedCount) / Double(requests.count)
                                    self.progress = String(format: "%.1f%%", progress * 100)
                                }
                            }
                            
                            // 在每批之间略微延迟，让用户能看到更新过程
                            try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
                        }
                    }
                }
            }
            }
            .frame(maxHeight: .infinity)
            .onAppear {
                // print("视图出现，设置播放器观察器")
                setupPlayerObserver()
            }
            .onDisappear {
                print("视图消失，清理观察器")
                cleanup()
            }
            .onChange(of: player) { newPlayer in
                if let player = newPlayer {
                    // print("播放器已更新，重新设置观察器")
                    setupPlayerObserver()
                } else {
                    print("播放器已清空")
                }
            }

            // 🎯 浮动底部导航栏 - 类似Cursor编辑器
            if showSuggestionNavigation && totalSuggestions > 0 {
                VStack {
                    Spacer()

                    HStack(spacing: 12) {
                        // 建议计数显示
                        HStack(spacing: 4) {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.orange)

                            Text("\(currentSuggestionIndex + 1) of \(totalSuggestions)")
                                .font(.system(size: 13, weight: .medium))
                                .foregroundColor(.white)
                        }

                        // 分隔线
                        Rectangle()
                            .frame(width: 1, height: 16)
                            .foregroundColor(.white.opacity(0.3))

                        // 导航箭头
                        HStack(spacing: 8) {
                            Button(action: {
                                navigateToPreviousSuggestion()
                            }) {
                                Image(systemName: "chevron.up")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(.white)
                                    .frame(width: 24, height: 24)
                                    .background(Color.white.opacity(0.1))
                                    .cornerRadius(4)
                            }
                            .buttonStyle(.plain)
                            .disabled(currentSuggestionIndex <= 0)

                            Button(action: {
                                navigateToNextSuggestion()
                            }) {
                                Image(systemName: "chevron.down")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(.white)
                                    .frame(width: 24, height: 24)
                                    .background(Color.white.opacity(0.1))
                                    .cornerRadius(4)
                            }
                            .buttonStyle(.plain)
                            .disabled(currentSuggestionIndex >= totalSuggestions - 1)
                        }

                        // 分隔线
                        Rectangle()
                            .frame(width: 1, height: 16)
                            .foregroundColor(.white.opacity(0.3))

                        // 批量操作按钮
                        HStack(spacing: 8) {
                            Button(action: {
                                acceptAllSuggestions()
                            }) {
                                HStack(spacing: 4) {
                                    Image(systemName: "checkmark")
                                        .font(.system(size: 11, weight: .medium))
                                    Text("全部接受")
                                        .font(.system(size: 12, weight: .medium))
                                }
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.green.opacity(0.8))
                                .cornerRadius(6)
                            }
                            .buttonStyle(.plain)

                            Button(action: {
                                ignoreAllSuggestions()
                            }) {
                                HStack(spacing: 4) {
                                    Image(systemName: "xmark")
                                        .font(.system(size: 11, weight: .medium))
                                    Text("全部忽略")
                                        .font(.system(size: 12, weight: .medium))
                                }
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.gray.opacity(0.8))
                                .cornerRadius(6)
                            }
                            .buttonStyle(.plain)
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.black.opacity(0.8))
                            .shadow(color: .black.opacity(0.3), radius: 8, x: 0, y: 4)
                    )
                    .padding(.bottom, 20)
                }
                .transition(.opacity.combined(with: .move(edge: .bottom)))
            }
        }
    }

    // 提取的函数
    private func createSegmentView(for segment: EditableSegment, at index: Int) -> some View {
        SegmentView(
            segment: segment,
            isSelected: segment.id == selectedSegmentId,
            isLastSegment: index == optimizedSegments.count - 1,
            cursorPosition: $cursorPosition,
            onSelect: { selectSegment(segment.id) },
            onSplit: { wordIndex in
                splitSegmentAtIndex(wordIndex, in: index)
            },
            onMerge: {
                mergeSegments(at: index)
            },
            onTimeClick: { time in
                handleTimeClick(time)
            },
            onUpdateText: { newText in
                // ✅ 核心修正：不再对 .text 赋值，而是调用辅助函数更新 .words 数组
                if index < self.optimizedSegments.count {
                    let oldSegment = self.optimizedSegments[index]
                    let newWords = self.createWords(from: newText, basedOn: oldSegment)
                    self.optimizedSegments[index].words = newWords
                }
                optimizedSegments[index].updateText(newText)
            },
            onUpdateTranslation: { newText in
                optimizedSegments[index].translatedText = newText
            },
            onEdit: { },
            onAcceptSuggestion: { suggestion in
                self.handleAcceptSuggestion(segmentId: segment.id, suggestion: suggestion)
            },
            // ✅ 传递新的“忽略”回调
            onIgnoreSuggestion: { suggestion in
                self.handleIgnoreSuggestion(segmentId: segment.id, suggestion: suggestion)
            },
            onReTranscribe: { onReTranscribe(segment) }
        )
        .overlay(
            Button(action: {
                deleteSegment(at: index) // 调用删除函数
            }) {
                Image(systemName: "trash")
                    .frame(width: 37)
                    .foregroundColor(.red) // 将"trash"图变成红色
                    .cornerRadius(5) // 设置圆角  

            }  
            .controlSize(.small)    
            .padding(25),
            alignment: .topLeading// 将删除按钮放在左上角
        )
        .listRowBackground(
            enableAutoScroll 
            ? (getCurrentSegmentIndex() == index ? Color.blue.opacity(0.1) : Color.clear) 
            : (shortSegmentIndices.contains(index) ? Color.red.opacity(0.2) : Color.clear)
        )
    }

    // MARK: 1. 新增的核心辅助函数
    /// 根据一个新的文本字符串和旧的字幕段落，重新计算并生成一个新的 [EditableWord] 数组。
    /// 这个函数是解决“只读属性”问题的关键，它通过修改底层的 `words` 数组来更新文本。
    /// - Parameters:
    ///   - newText: 用户输入或AI修正后的新文本。
    ///   - oldSegment: 原始的字幕段落，用于获取时间戳范围。
    /// - Returns: 一个包含新单词和重新计算后时间戳的数组。
    private func createWords(from newText: String, basedOn oldSegment: EditableSegment) -> [EditableWord] {
        let newWordsArray = newText.split(separator: " ").map(String.init)
        
        guard !newWordsArray.isEmpty else {
            return []
        }

        let totalDuration = oldSegment.endTime - oldSegment.startTime
        
        // 处理时长为0或负数的异常情况
        guard totalDuration > 0 else {
            var newEditableWords: [EditableWord] = []
            for (index, word) in newWordsArray.enumerated() {
                let defaultDuration = 0.1 // 为每个单词分配一个默认的短时长
                let wordStart = oldSegment.startTime + (Double(index) * defaultDuration)
                newEditableWords.append(
                    EditableWord(word: String(word), start: wordStart, end: wordStart + defaultDuration, probability: 1.0)
                )
            }
            return newEditableWords
        }

        // 根据单词数量，将总时长平均分配给每个新单词
        let wordDuration = totalDuration / Double(newWordsArray.count)
        
        var newEditableWords: [EditableWord] = []
        for (index, word) in newWordsArray.enumerated() {
            let wordStart = oldSegment.startTime + (Double(index) * wordDuration)
            let wordEnd = wordStart + wordDuration
            newEditableWords.append(
                EditableWord(
                    word: String(word),
                    start: wordStart,
                    end: min(wordEnd, oldSegment.endTime), // 确保不超过片段总结束时间
                    probability: 1.0 // 用户确认的文本，置信度为1.0
                )
            )
        }
        return newEditableWords
    }

    // MARK: 3. 修正后的 handleAcceptSuggestion 函数
    /// 处理用户接受校对建议的操作。
    private func handleAcceptSuggestion(segmentId: UUID, suggestion: CorrectionSuggestion) {
        guard let index = optimizedSegments.firstIndex(where: { $0.id == segmentId }) else { return }
        
        let oldSegment = optimizedSegments[index]
        let newText = oldSegment.text.replacingOccurrences(of: suggestion.originalText, with: suggestion.correctedText)
        
        // ✅ 核心修正：调用辅助函数，用新文本生成新的 words 数组并赋值
        let newWords = self.createWords(from: newText, basedOn: oldSegment)
        self.optimizedSegments[index].words = newWords
        
        // 移除建议的逻辑保持不变
        self.optimizedSegments[index].correctionSuggestions?.removeAll(where: { $0.id == suggestion.id })
        
        if self.optimizedSegments[index].correctionSuggestions?.isEmpty == true {
            self.optimizedSegments[index].correctionSuggestions = nil
        }
        
        print("接受建议: '\(suggestion.originalText)' -> '\(suggestion.correctedText)'")
    }

    // ✅ 新增处理“忽略”操作的函数
    private func handleIgnoreSuggestion(segmentId: UUID, suggestion: CorrectionSuggestion) {
        guard let index = optimizedSegments.firstIndex(where: { $0.id == segmentId }) else { return }
        
        // 只从建议列表中移除这条建议
        optimizedSegments[index].correctionSuggestions?.removeAll(where: { $0.id == suggestion.id })
        
        // 如果所有建议都处理完了，同样清空数组以移除高亮
        if optimizedSegments[index].correctionSuggestions?.isEmpty == true {
            optimizedSegments[index].correctionSuggestions = nil
        }
        
        print("忽略建议: '\(suggestion.originalText)'")
    }

    // 删除段落的函数
    private func deleteSegment(at index: Int) {
        guard index >= 0 && index < optimizedSegments.count else { return }
        optimizedSegments.remove(at: index)
        // 可选：更新选中状态
        selectedSegmentId = nil
    }

    // 处理时间点击的函数
    private func handleTimeClick(_ time: Double) {
        if let currentPlayer = player {
            let targetTime = CMTime(seconds: time, preferredTimescale: 1000)
            currentPlayer.pause()
            currentPlayer.seek(to: targetTime, toleranceBefore: .zero, toleranceAfter: .zero) { [self] finished in
                if finished {
                    currentTime = time
                }
            }
        } else {
            print("错误：播放器未初始化，当前 player 状态：\(String(describing: player))")
        }
    }
    
    // 更新翻译配置
    private func updateTranslationConfiguration() {
        configuration = TranslationSession.Configuration(
            source: .init(identifier: transcriptionLanguage),
            target: .init(identifier: aiTargetLanguage)
        )
    }
    
    // 更新段落文本
    func updateSegmentText(_ segmentId: UUID, newText: String) {
        // 将文本按行分割
        let lines = newText.components(separatedBy: .newlines)
            .filter { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }
        
        // 每5行一批进行更新
        let batchSize = 5
        for i in stride(from: 0, to: lines.count, by: batchSize) {
            let end = min(i + batchSize, lines.count)
            let batchLines = Array(lines[i..<end])
            
            // 更新当前批次的文本
            if let index = optimizedSegments.firstIndex(where: { $0.id == segmentId }) {
                var segment = optimizedSegments[index]
                let currentText = segment.text
                let updatedText = batchLines.joined(separator: "\n")
                
                // 如果当前文本不为空，添加换行符
                let finalText = currentText.isEmpty ? updatedText : currentText + "\n" + updatedText
                segment.updateText(finalText)
                optimizedSegments[index] = segment
                
                // 每批更新后添加短暂延迟
                if end < lines.count {
                    Thread.sleep(forTimeInterval: 0.1)
                }
            }
        }
    }
    
    // 更新翻译结果
    private func updateTranslation(response: TranslationSession.Response) {
        guard let index = optimizedSegments.firstIndex(where: { $0.id.uuidString == response.clientIdentifier }) else {
            return
        }
        
        // 在保存翻译结果时处理格式
        let formattedText = response.targetText
            .components(separatedBy: .whitespaces)
            .filter { !$0.isEmpty }
            .joined(separator: " ")
            .trimmingCharacters(in: .whitespaces)
        
        // 更新翻译结果
        optimizedSegments[index].translatedText = formattedText
    }
    
    // 设置定时器监听播放时间
    private func setupTimer() {
        // print("设置定时器，自动滚动状态: \(enableAutoScroll)")
        // 清除现有定时器
        timer?.invalidate()
        timer = nil
        
        // 不再创建新的 Timer，而是依赖播放器的时间观察器
        if let player = self.player {
            self.currentTime = CMTimeGetSeconds(player.currentTime())
        }
    }
    
    // 获取当前播放时间所在的段落索引
    private func getCurrentSegmentIndex() -> Int? {
        // 使用简单缓存避免频繁重新计算
        return optimizedSegments.firstIndex { segment in
            let segmentStart = segment.startTime
            let segmentEnd = segment.endTime
            return currentTime >= segmentStart && currentTime <= segmentEnd
        }
    }
    
    // 处理步骤描述
    private var currentStepDescription: String {
        switch currentStep {
        case .none:
            return ""
        case .correcting:
            return "AI正在对文本进行优化..."
        case .segmenting:
            return "AI正在进行智能断句..."
        // case .summarizing:
        //     return "AI正在生成字幕主题摘要..."
        case .translating:
            return "AI正在进行翻译..."
        }
    }

    // MARK: - AI校对主流程函数
    private func processProofreading() async {
        // 确保有可用字幕
        guard !optimizedSegments.isEmpty else {
            let alert = NSAlert()
            alert.messageText = "无可用字幕"
            alert.informativeText = "请添加视频并转录以生成字幕。"
            alert.addButton(withTitle: "确定")
            alert.runModal()
            return
        }
        
        // --- 设置UI状态 ---
        isProcessing = true
        currentStep = .correcting // 您可以复用现有的状态，或创建一个新的
        progress = "AI校对准备中..."
        // 使用defer确保任务结束时恢复UI状态
        defer {
            isProcessing = false
            currentStep = .none
        }

        do {
            // --- 调用核心校对逻辑 ---
            // 调用我们之前重构好的、透明化的校对函数
            let segmentsWithSuggestions = try await correctSubtitles(optimizedSegments)
            
            // --- 更新UI ---
            // 将带有建议的结果更新回主字幕列表
            await MainActor.run {
                self.optimizedSegments = segmentsWithSuggestions
            }

            // 校对完成后给用户一个明确的提示
            let suggestionCount = segmentsWithSuggestions.flatMap { $0.correctionSuggestions ?? [] }.count

            await MainActor.run {
                // 🎯 更新建议导航状态
                updateSuggestionNavigation(suggestions: suggestionCount)
                let alert = NSAlert()
                alert.messageText = "AI 校对完成"
                alert.informativeText = "已在文本中高亮显示 \(suggestionCount) 处建议修改。"

                // 添加 "全部接受" 按钮
                alert.addButton(withTitle: "全部接受")

                // 添加 "全部忽略" 按钮
                alert.addButton(withTitle: "全部忽略")

                // 添加 "好的" 按钮
                alert.addButton(withTitle: "好的") //Keep a "好的" button for dismissing without action

                // Run the alert and handle the response
                let response = alert.runModal()

                // 处理用户选择
                Task {
                    switch response {
                    case .alertFirstButtonReturn: // "全部接受"
                        await acceptAllSuggestions()
                    case .alertSecondButtonReturn: // "全部忽略"
                        await ignoreAllSuggestions()
                    default:
                        // "好的" or window closed: do nothing
                        break
                    }
                }
            }
            
        } catch {
            print("❌ AI校对流程失败: \(error)")
            // 在此可以向用户显示一个错误提示弹窗
            await MainActor.run {
                let alert = NSAlert()
                alert.messageText = "校对失败"
                alert.informativeText = "AI校对过程中发生错误：\(error.localizedDescription)"
                alert.addButton(withTitle: "确定")
                alert.runModal()
            }
        }
    }

    //接受所有的 AI 建议
    private func acceptAllSuggestions() async {
        await MainActor.run {
            for index in optimizedSegments.indices {
                if let suggestions = optimizedSegments[index].correctionSuggestions {
                    for suggestion in suggestions {
                        handleAcceptSuggestion(segmentId: optimizedSegments[index].id, suggestion: suggestion)
                    }
                }
            }
        }
    }

    //忽略所有的 AI 建议
    private func ignoreAllSuggestions() async {
           await MainActor.run {
               for index in optimizedSegments.indices {
                   if let suggestions = optimizedSegments[index].correctionSuggestions {
                       optimizedSegments[index].correctionSuggestions = nil
                   }
               }
           }
       }
    
    // AI断句优化处理流程 - 验证专业版功能
    private func processSegmentation() async {
        // 确保有专业版权限
        guard LicenseManager.shared.isPro() else {
            ProFeatureHelper.shared.showProFeatureAlert()
            return
        }

        // 首先检查是否有可用字幕
        guard !optimizedSegments.isEmpty else {
            let alert = NSAlert()
            alert.messageText = "无可用字幕"
            alert.informativeText = "请添加视频并转录以生成字幕。"
            alert.addButton(withTitle: "确定")
            alert.runModal()
            return
        }
        
        isProcessing = true
        defer { isProcessing = false }
        
        do {
            // 先进行文本纠错
            // currentStep = .correcting
            // print("AI正在对文本进行优化...")
            // let correctedSegments = try await correctSubtitles(originalSegments)
            // print("✓ 字幕校正完成，共 \(correctedSegments.count) 行")

            // 然后进行断句优化  1. 组织成字幕行
            currentStep = .segmenting
            let sentenceSegments = organizeWordsIntoSentences(optimizedSegments)
            
            // 初始化进度追踪
            totalBatches = sentenceSegments.count
            completedBatches = 0
            progress = "处理进度: 0/\(totalBatches)"
            
            var optimizedSegments: [EditableSegment] = []
            
            // 处理每个段落
            for (index, segment) in sentenceSegments.enumerated() {
                let wordCount = segment.words.count
                
                    // 更新进度
                completedBatches = index + 1
                progress = "处理进度: \(completedBatches)/\(totalBatches)"
                
                // 如果句子中单词数不超过16个，直接添加
                if wordCount <= 16 {
                    optimizedSegments.append(segment)
                    continue
                }
                
                // 如果句子中单词数超过14个，使用LLM断句
                // print("处理长句子，单词数: \(wordCount)")
                
                do {
                    let text = segment.words.map { $0.word }.joined(separator: " ")
                    let messages: [[String: Any]] = [
                        ["role": "system", "content": """
                        You are a subtitle segmentation expert. Break down long \(self.transcriptionLanguage) sentences into shorter, semantically complete segments for subtitles. Each segment should ideally be **under 16 words** and must not exceed **18 words**.

                        WARNING: You can ONLY add <br> tags for sentence breaks. DO NOT modify, delete, or add any other content! All original words and punctuation must be preserved.

                        Processing Rules:
                        1.  **Target Length & Limit:** Aim for segments under 16 words. If a sentence segment is still longer, break it further ensuring no segment exceeds 14 words.
                        2.  **Punctuation Priority:** If the sentence contains punctuation marks (commas, semicolons, etc.), prioritize breaking *immediately before or after* these marks (usually after a comma, before a conjunction following punctuation). Keep punctuation with its original adjacent word.
                        3.  **Breaking Without Punctuation:** In the absence of suitable punctuation, use your motherlanguage \(self.aiTargetLanguage) understanding to insert `<br>` at natural grammatical pauses. Prioritize breaking:
                            *   Before coordinating conjunctions (like `and`, `but`, `or`, `so`) that connect independent clauses.
                            *   Between clauses (e.g., before relative pronouns like `that`, `which`, `who` if it makes sense semantically).
                            *   After complete prepositional phrases or other natural phrasal units.
                            *   As a last resort, between the subject and verb if the subject phrase is very long.
                        4.  **Semantic Integrity:** Each segment after breaking should retain as much complete meaning as possible within the length constraints.
                        5.  **Multiple Breaks:** Long sentences may require multiple `<br>` tags.

                        Here's an example:
                        Input: Although the initial investment might seem significant, it will pay off in the long run because the market is growing rapidly.
                        Output: Although the initial investment might seem significant,<br>it will pay off in the long run<br>because the market is growing rapidly.

                        Another example (no punctuation):
                        Input: We need to develop a comprehensive strategy ensuring sustainable growth over the next decade.
                        Output: We need to develop a comprehensive strategy<br>ensuring sustainable growth over the next decade.

                        Process the following text:
                        """],
                        ["role": "user", "content": text]
                    ]
                    
                    let aiResult = try await callAI(messages: messages, maxTokens: 4096)
                    
                    // 验证AI结果
                    let aiTextOnly = aiResult.replacingOccurrences(of: "<br>", with: " ")
                                            .replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
                                            .trimmingCharacters(in: .whitespacesAndNewlines)
                    
                    let originalTextOnly = text.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
                                                .trimmingCharacters(in: .whitespacesAndNewlines)
                    
                    if aiResult.contains("<br>") && aiTextOnly.count >= originalTextOnly.count * Int(0.95) {
                        // 将AI断句结果转换为新的段落，保留原始单词时间戳
                        let subSegments = aiResult.components(separatedBy: "<br>")
                            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
                            .filter { !$0.isEmpty }
                        
                        // print("AI断句将句子分割为 \(subSegments.count) 个子句")
                        
                        // 保存原始单词及时间戳
                        let originalWords = segment.words
                        var currentWordIndex = 0
                        
                        for subSegment in subSegments {
                            // 分割子句文本为单词
                            let subSegmentWords = subSegment.split(separator: " ")
                                .map { String($0) }
                            
                            var subSegmentEditableWords: [EditableWord] = []
                            var wordsMapped = 0
                            
                            // 尝试匹配原始单词并保留时间戳
                            for subWord in subSegmentWords {
                                // 清理单词以便匹配（移除标点等）
                                let cleanSubWord = subWord.trimmingCharacters(in: .punctuationCharacters).lowercased()
                                
                                // 如果当前索引已超出原始单词范围，使用最后一个单词的时间
                                if currentWordIndex >= originalWords.count {
                                    let lastWord = originalWords.last!
                                    subSegmentEditableWords.append(
                                        EditableWord(
                                            word: subWord,
                                            start: lastWord.end - 0.1,
                                            end: lastWord.end,
                                            probability: 1.0
                                        )
                                    )
                                    continue
                                }
                                
                                // 获取当前原始单词
                                var originalWord = originalWords[currentWordIndex]
                                let cleanOriginalWord = originalWord.word.trimmingCharacters(in: .punctuationCharacters).lowercased()
                                
                                // 尝试匹配当前单词
                                if cleanSubWord == cleanOriginalWord {
                                    // 完全匹配，使用原始时间戳
                                    subSegmentEditableWords.append(
                                        EditableWord(
                                            word: subWord, // 使用断句后的单词（保留大小写和标点）
                                            start: originalWord.start,
                                            end: originalWord.end,
                                            probability: originalWord.probability
                                        )
                                    )
                                    currentWordIndex += 1
                                    wordsMapped += 1
                                } else {
                                    // 查找前向匹配
                                    var found = false
                                    let lookAheadLimit = 3
                                    
                                    for lookAhead in 0..<min(lookAheadLimit, originalWords.count - currentWordIndex) {
                                        let aheadWord = originalWords[currentWordIndex + lookAhead]
                                        let cleanAheadWord = aheadWord.word.trimmingCharacters(in: .punctuationCharacters).lowercased()
                                        
                                        if cleanSubWord == cleanAheadWord {
                                            // 找到匹配，使用这个单词的时间戳
                                            subSegmentEditableWords.append(
                                                EditableWord(
                                                    word: subWord,
                                                    start: aheadWord.start,
                                                    end: aheadWord.end,
                                                    probability: aheadWord.probability
                                                )
                                            )
                                            currentWordIndex = currentWordIndex + lookAhead + 1
                                            wordsMapped += 1
                                            found = true
                                            break
                                        }
                                    }
                                    
                                    if !found {
                                        // 无法匹配，使用估计时间戳
                                        if currentWordIndex < originalWords.count {
                                            // 使用当前原始单词的时间戳
                                            subSegmentEditableWords.append(
                                                EditableWord(
                                                    word: subWord,
                                                    start: originalWord.start,
                                                    end: originalWord.end,
                                                    probability: 1.0
                                                )
                                            )
                                            currentWordIndex += 1
                                        } else if let lastWord = subSegmentEditableWords.last {
                                            // 基于最后添加的单词估计时间戳
                                            let wordDuration = 0.2 // 默认单词持续时间
                                            subSegmentEditableWords.append(
                                                EditableWord(
                                                    word: subWord,
                                                    start: lastWord.end,
                                                    end: lastWord.end + wordDuration,
                                                    probability: 1.0
                                                )
                                            )
                                        } else {
                                            // 实在没有参考，使用段落开始时间
                                            subSegmentEditableWords.append(
                                                EditableWord(
                                                    word: subWord,
                                                    start: segment.startTime,
                                                    end: segment.startTime + 0.3,
                                                    probability: 1.0
                                                )
                                            )
                                        }
                                    }
                                }
                            }
                            
                            // print("子句: \(subSegment) - 成功映射 \(wordsMapped)/\(subSegmentWords.count) 个单词")
                            
                            // 创建新段落并添加
                            let newSegment = EditableSegment(words: subSegmentEditableWords)
                            optimizedSegments.append(newSegment)
                        }
                        
                        // print("✓ LLM断句成功，并保留了原始时间戳")
                    } else {
                        // 使用备用断句，同样要保留原始时间戳
                        let fallbackResult = semanticFallbackSegmentation(text)
                        let subSegments = fallbackResult.components(separatedBy: "<br>")
                            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
                            .filter { !$0.isEmpty }
                        
                        // 保存原始单词及时间戳
                        let originalWords = segment.words
                        var currentWordIndex = 0
                        
                        for subSegment in subSegments {
                            // 分割子句文本为单词
                            let subSegmentWords = subSegment.split(separator: " ")
                                .map { String($0) }
                            
                            var subSegmentEditableWords: [EditableWord] = []
                            
                            // 尝试匹配原始单词并保留时间戳
                            for subWord in subSegmentWords {
                                // 如果当前索引已超出原始单词范围，使用估计时间戳
                                if currentWordIndex >= originalWords.count {
                                    if let lastWord = subSegmentEditableWords.last {
                                        // 基于最后添加的单词估计时间戳
                                        let wordDuration = 0.2 // 默认单词持续时间
                                        subSegmentEditableWords.append(
                                            EditableWord(
                                                word: subWord,
                                                start: lastWord.end,
                                                end: lastWord.end + wordDuration,
                                                probability: 1.0
                                            )
                                        )
                                    } else {
                                        // 实在没有参考，使用段落结束时间
                                        subSegmentEditableWords.append(
                                            EditableWord(
                                                word: subWord,
                                                start: segment.endTime - 0.3,
                                                end: segment.endTime,
                                                probability: 1.0
                                            )
                                        )
                                    }
                                    continue
                                }
                                
                                // 获取当前原始单词
                                let originalWord = originalWords[currentWordIndex]
                                
                                // 简单映射，不尝试匹配单词内容
                                subSegmentEditableWords.append(
                                    EditableWord(
                                        word: subWord,
                                        start: originalWord.start,
                                        end: originalWord.end,
                                        probability: originalWord.probability
                                    )
                                )
                                currentWordIndex += 1
                            }
                            
                            // 创建新段落并添加
                            let newSegment = EditableSegment(words: subSegmentEditableWords)
                            optimizedSegments.append(newSegment)
                        }
                        
                        // print("! LLM断句不理想，使用备用断句并保留时间戳")
                    }
                } catch {
                    // 使用备用断句，也要尝试保留原始时间戳
                    let fallbackResult = semanticFallbackSegmentation(segment.words.map { $0.word }.joined(separator: " "))
                    let subSegments = fallbackResult.components(separatedBy: "<br>")
                        .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
                        .filter { !$0.isEmpty }
                    
                    // 计算每个子句应该对应的原始单词数量
                    let originalWords = segment.words
                    let totalWords = originalWords.count
                    let wordsPerSegment = max(1, totalWords / subSegments.count)
                    
                    for (index, subSegment) in subSegments.enumerated() {
                        // 确定当前子句对应的原始单词范围
                        let startIndex = min(index * wordsPerSegment, totalWords)
                        let endIndex = min(startIndex + wordsPerSegment, totalWords)
                        
                        // 如果范围有效，使用该范围内的原始单词
                        if startIndex < endIndex {
                            let subsetWords = Array(originalWords[startIndex..<endIndex])
                            let subSegmentWords = subSegment.split(separator: " ").map { String($0) }
                            
                            // 创建新的EditableWord数组，尽量保持原始时间戳
                            var editableWords: [EditableWord] = []
                            
                            if subSegmentWords.count <= subsetWords.count {
                                // 如果子句单词数少于或等于原始单词数，直接映射
                                for (wordIndex, word) in subSegmentWords.enumerated() {
                                    editableWords.append(
                                        EditableWord(
                                            word: word,
                                            start: subsetWords[wordIndex].start,
                                            end: subsetWords[wordIndex].end,
                                            probability: subsetWords[wordIndex].probability
                                        )
                                    )
                                }
                            } else {
                                // 如果子句单词数多于原始单词数，需要插值
                                let originalStartTime = subsetWords.first!.start
                                let originalEndTime = subsetWords.last!.end
                                let totalDuration = originalEndTime - originalStartTime
                                
                                for (wordIndex, word) in subSegmentWords.enumerated() {
                                    let progress = Double(wordIndex) / Double(subSegmentWords.count - 1)
                                    let wordStart = originalStartTime + progress * totalDuration
                                    let wordEnd = wordStart + (totalDuration / Double(subSegmentWords.count))
                                    
                                    editableWords.append(
                                        EditableWord(
                                            word: word,
                                            start: wordStart,
                                            end: min(wordEnd, originalEndTime),
                                            probability: 1.0
                                        )
                                    )
                                }
                            }
                            
                            optimizedSegments.append(EditableSegment(words: editableWords))
                        }
                    }
                    
                    // print("❌ LLM调用失败，使用备用断句并尽量保留时间戳")
                }
            }
            
            // 更新UI
            await MainActor.run {
                self.optimizedSegments = optimizedSegments
            }
            
        } catch {
            // print("❌ 断句处理失败: \(error.localizedDescription)")
            progress = "处理失败: \(error.localizedDescription)"
        }

        currentStep = .none
    }

    // 为了解析AI返回的带行号的建议，我们创建一个临时的内部结构体
    struct CorrectionSuggestionWithIndex: Codable {
        let lineIndex: Int
        let originalText: String
        let correctedText: String
        let category: String

        enum CodingKeys: String, CodingKey {
            case lineIndex = "line_index"
            case originalText = "original_text"
            case correctedText = "corrected_text"
            case category
        }
    }

    // AI纠错函数
    // 此函数不再直接修改文本，而是找出错误并作为“建议”附加到对应的字幕行上。
    // - Parameter segments: 需要校对的原始字幕段落。
    // - Returns: 一个新的字幕段落数组，其中包含错误的行已经被附加了校对建议。
    private func correctSubtitles(_ segments: [EditableSegment]) async throws -> [EditableSegment] {
        
        // 1. 创建一个可修改的副本，用于附加建议
        var segmentsWithSuggestions = segments

        let batchSize = 80
        
        // 初始化进度追踪
        totalBatches = (segments.count + batchSize - 1) / batchSize
        completedBatches = 0
        progress = "文本纠错处理: 0/\(totalBatches)"
        
        let batches = stride(from: 0, to: segments.count, by: batchSize).map {
            (startIndex: $0, batch: Array(segments[$0..<min($0 + batchSize, segments.count)]))
        }

        // 使用任务组进行并发处理
        try await withThrowingTaskGroup(of: (Int, [CorrectionSuggestionWithIndex]).self) { group in
            let taskSemaphore = AsyncSemaphore(value: MAX_THREADS)
            
            for (startIndex, currentBatch) in batches {
                await taskSemaphore.wait()
                
                group.addTask {
                    
                    // --- 核心修改：全新的Prompt ---
                    let batchText = currentBatch.enumerated().map { "[\($0)] \($1.text)" }.joined(separator: "\n")
                    
                    let messages: [[String: Any]] = [
                        ["role": "system", "content": """
                        背景：由AI转录的稿件存在大量错误，尤其是在人名、公司名以及关键技术术语上。
                        角色：你是一名十分严谨的文本校对员。
                        任务：对字幕稿进行了逐句校对和修正。

                        **绝对规则：**
                        - **【禁止一切额外修改】**：绝对禁止进行任何语法润色、风格优化或句子重组。
                        - **【返回修改清单】**：必须以一个JSON数组的格式返回所有发现的修正。数组中的每个对象都应包含它在原始批次中的行索引`line_index`。
                        - **【无错误则返回空数组】**：如果未发现任何符合条件的错误，必须返回一个空数组 `[]`。
                        """],
                        ["role": "user", "content": """
                        请根据以下信息校对文本：
                        
                        【前情提要】
                        \(manualKeywords)
                        
                        【输出格式】
                        [
                          {
                            "line_index": 0,
                            "original_text": "错误的原文片段",
                            "corrected_text": "建议修改成的文本",
                            "category": "错别字 或 专业术语"
                          }
                        ]

                        【待校对的文本批次】
                        \(batchText)
                        """]
                    ]

                    let responseJsonString = try await callAI(messages: messages, maxTokens: 2000)
                    guard let jsonData = responseJsonString.data(using: .utf8) else {
                        print("批次 \(startIndex / batchSize + 1) AI响应无法转换为Data")
                        return (startIndex, [])
                    }
                    
                    // 解码AI返回的建议清单
                    var suggestions: [CorrectionSuggestionWithIndex] = []
                    do {
                        // --- 增强的JSON提取逻辑 ---
                        // AI有时会返回被文本或markdown包围的JSON。我们先提取出`[`和`]`之间的纯净JSON数组。
                        var stringToDecode = responseJsonString
                        if let startIndex = responseJsonString.firstIndex(of: "["),
                           let endIndex = responseJsonString.lastIndex(of: "]") {
                            stringToDecode = String(responseJsonString[startIndex...endIndex])
                        }

                        if let cleanJsonData = stringToDecode.data(using: .utf8) {
                            suggestions = try JSONDecoder().decode([CorrectionSuggestionWithIndex].self, from: cleanJsonData)
                        } else {
                             print("❌ 批次 \(startIndex / batchSize + 1) 清理后的JSON字符串无法转换为Data")
                        }
                    } catch {
                        // 当解码失败时，打印详细的错误和AI返回的原始字符串，这是调试的关键！
                        print("❌ 批次 \(startIndex / batchSize + 1) JSON解码失败: \(error.localizedDescription)")
                        print("   收到的原始字符串: \(responseJsonString)")
                        // 返回一个空数组，让该批次安全地跳过校对，而不是让整个流程失败。
                    }
                    
                    await MainActor.run {
                        self.completedBatches += 1
                        self.updateProgress()
                    }
                    
                    await taskSemaphore.signal()
                    
                    return (startIndex, suggestions)
                }
            }
            
            // --- 收集并应用建议 ---
            for try await (batchStartIndex, suggestionsInBatch) in group {
                // 将建议按行号分组
                let suggestionsByLine = Dictionary(grouping: suggestionsInBatch, by: { $0.lineIndex })
                
                // 遍历批次中的每一行，附加建议
                for (localIndex, _) in batches.first(where: { $0.startIndex == batchStartIndex })!.batch.enumerated() {
                    let globalIndex = batchStartIndex + localIndex
                    
                    if let suggestionsForThisLine = suggestionsByLine[localIndex] {
                        // 转换为不带索引的 CorrectionSuggestion
                        let finalSuggestions = suggestionsForThisLine.map {
                            CorrectionSuggestion(originalText: $0.originalText, correctedText: $0.correctedText, category: $0.category)
                        }
                        
                        // 附加到对应的字幕行上
                        segmentsWithSuggestions[globalIndex].correctionSuggestions = finalSuggestions
                        print("✅ 在第 \(globalIndex) 行发现建议: \(finalSuggestions.map { $0.originalText })")
                    }
                }
            }
        }
        
        return segmentsWithSuggestions
    }

    // AI翻译优化处理流程 - 验证专业版功能
    private func processTranslation() async {
        // 确保有专业版权限
        guard LicenseManager.shared.isPro() else {
            ProFeatureHelper.shared.showProFeatureAlert()
            return
        }

        // 首先检查是否有可用字幕
        guard !optimizedSegments.isEmpty else {
            let alert = NSAlert()
            alert.messageText = "无可用字幕"
            alert.informativeText = "请添加视频并转录以生成字幕。"
            alert.addButton(withTitle: "确定")
            alert.runModal()
            return
        }
        
        isProcessing = true
        defer { isProcessing = false }
        
        do {
            currentStep = .translating
            
            // 选择要翻译的文本源
            let segmentsToTranslate = !optimizedSegments.isEmpty ? optimizedSegments : originalSegments
            print("使用\(!optimizedSegments.isEmpty ? "优化后" : "原始")字幕进行翻译，共 \(segmentsToTranslate.count) 行")

            // 获取需要翻译的文本数组
            let textsToTranslate = segmentsToTranslate.map { ($0.text, $0.startTime, $0.endTime) }
            print("textsToTranslate: \(textsToTranslate)")

            // 生成摘要
            let summary = try await generateSummary(from: textsToTranslate )
            let keywords = try await generateKeywords(from: textsToTranslate)
            print("生成摘要: \(summary)")
            print("生成关键词: \(keywords)")
            
            // 执行翻译
            // 直接调用新的两阶段翻译流程
            let finalResults = try await translateSubtitles(
                textsToTranslate,
                summary: summary,
                keywords: keywords
            )
            // print("translatedResults: \(translatedResults)")
            // 更新UI
            await MainActor.run {
                optimizedSegments = formatFinalSubtitles(finalResults)
            }
            
        } catch {
            // print("❌ 翻译处理失败: \(error)")
        }
        currentStep = .none
    }

    // 语义感知的备用断句方法
    private func semanticFallbackSegmentation(_ text: String) -> String {
        // print("执行语义备用断句方法，文本长度: \(text.count)")
        
        // 检查语言类型
        let isLatinBased = ["en", "fr", "es", "de", "it", "pt"].contains(transcriptionLanguage)
        
        if isLatinBased {
            return latinSemanticSegmentation(text)
        } else {
            return cjkSemanticSegmentation(text)
        }
    }
    
    // 拉丁语系的语义断句
    private func latinSemanticSegmentation(_ text: String) -> String {
        // 定义可能的断句位置的连接词和标点
        let breakPoints = [
            " and ", " but ", " or ", " nor ", " for ", " so ", " yet ",
            " because ", " although ", " though ", " since ", " unless ",
            " if ", " when ", " where ", " while ", " as ", " that ",
            "; ", ": "
        ]
        
        let words = text.split(separator: " ")
        var result = ""
        var currentLine = ""
        var wordCount = 0
        var currentIndex = 0
        
        while currentIndex < words.count {
            let word = words[currentIndex]
            let wordStr = String(word)
            
            // 添加当前单词到行
            if !currentLine.isEmpty {
                currentLine += " "
            }
            currentLine += wordStr
            wordCount += 1
            currentIndex += 1
            
            // 检查是否需要断句
            let shouldBreak = wordCount >= 14 || // 达到最大单词数
                              (wordCount >= 6 && breakPoints.contains { currentLine.contains($0) }) || // 在连接词处断句
                              (wordStr.hasSuffix(".") || wordStr.hasSuffix("!") || wordStr.hasSuffix("?")) // 在句子结束标点处断句
            
            if shouldBreak || currentIndex == words.count {
                if !result.isEmpty {
                    result += "<br>"
                }
                result += currentLine
                currentLine = ""
                wordCount = 0
            }
        }
        
        return result
    }
    
    // 中日韩语言的语义断句
    private func cjkSemanticSegmentation(_ text: String) -> String {
        // 中文断句标点
        let breakPoints = ["。", "！", "？", "；", "：", "，"]
        
        var result = ""
        var currentLine = ""
        var charCount = 0
        
        for char in text {
            currentLine.append(char)
            charCount += 1
            
            // 检查是否在断句标点后
            let shouldBreak = breakPoints.contains(String(char)) || charCount >= 14
            
            if shouldBreak {
                if !result.isEmpty {
                    result += "<br>"
                }
                result += currentLine
                currentLine = ""
                charCount = 0
            }
        }
        
        // 添加最后一行(如果有)
        if !currentLine.isEmpty {
            if !result.isEmpty {
                result += "<br>"
            }
            result += currentLine
        }
        
        return result
    }

    // 修改 AI翻译的相关常量，更适合长视频处理
    private let BATCH_SIZE = 10  // 增加到20，在效率和质量之间取得平衡
    private let MAX_THREADS = 5 // 保持5个并行线程，避免API限制

    // 您可以把这个扩展放到文件的任何位置，或者直接将函数定义在View内部
    // 这是您需要替换的函数
    private func translateSubtitles(_ subtitles: [(text: String, start: Double, end: Double)], summary: String, keywords: String)
        async throws -> [(original: String, translated: String, start: Double, end: Double)] {
        
        if subtitles.isEmpty {
            print("警告: 没有字幕行需要翻译")
            return []
        }
        
        let contextSummary = if !manualKeywords.isEmpty {
            "\(summary)\n关键词：\(manualKeywords)"
        } else {
            summary
        }
        
        // --- 阶段一：批量初翻 ---
        // 目标：快速处理大部分字幕，并识别出格式有问题的批次
        print("--- 阶段一：开始批量初翻 ---")
        
        let batchSize = min(BATCH_SIZE, subtitles.count)
        let batches = stride(from: 0, to: subtitles.count, by: batchSize).map {
            Array(subtitles[$0..<min($0 + batchSize, subtitles.count)])
        }
        
        totalBatches = batches.count * 2 // 总进度条需要考虑两个阶段
        completedBatches = 0
        updateProgress()
        
        // --- CHANGE START: 使用一个中间结果数组来跟踪状态 ---
        // 我们需要一个更灵活的数据结构来标记需要精校的行
        var intermediateResults = subtitles.map {
            (original: $0.text, translated: "", start: $0.start, end: $0.end, needsRefinement: false)
        }
        // --- CHANGE END ---
        
        let maxConcurrent = min(MAX_THREADS, batches.count)
        
        await withTaskGroup(of: (batchIndex: Int, translations: [String]).self) { group in
            let taskSemaphore = AsyncSemaphore(value: maxConcurrent)
            
            for (batchIndex, batch) in batches.enumerated() {
                await taskSemaphore.wait()
                
                group.addTask {
                    
                    let numberedTexts = batch.enumerated().map { "[\($0+1)] \($1.text)" }
                    let messages = self.buildBatchMessages(texts: numberedTexts, contextSummary: contextSummary, keywords: keywords)
                    
                    // --- CHANGE START: 简化API调用和重试逻辑 ---
                    // 这里我们只关心能否获取到格式正确的批量结果
                    var translatedLines: [String] = []
                    let maxRetries = 3
                    for attempt in 0..<maxRetries {
                        do {
                            let translatedText = try await self.callAI(messages: messages, maxTokens: 4096)
                            let lines = translatedText.components(separatedBy: "\n").filter { !$0.isEmpty }
                            
                            // 严格检查行数是否匹配
                            if lines.count == batch.count {
                                translatedLines = lines // 成功，保存结果
                                print("批次 \(batchIndex+1) 初翻成功，行数匹配。")
                                break // 退出重试循环
                            } else {
                                print("批次 \(batchIndex+1) 行数不匹配 (返回\(lines.count)/需要\(batch.count))，尝试重试...")
                                if attempt == maxRetries - 1 {
                                    print("批次 \(batchIndex+1) 达到最大重试次数，标记为需要精校。")
                                }
                            }
                        } catch {
                            print("批次 \(batchIndex+1) API调用失败 (尝试 \(attempt+1)/\(maxRetries)): \(error)")
                        }
                        // 如果需要重试，可以加入短暂的等待
                        if translatedLines.isEmpty && attempt < maxRetries - 1 {
                            try? await Task.sleep(nanoseconds: 1_000_000_000)
                        }
                    }
                    // --- CHANGE END ---
                    
                    await MainActor.run {
                        self.completedBatches += 1
                        self.updateProgress()
                    }
                    
                    // 正确的代码 - 在这里添加 signal 调用
                    await taskSemaphore.signal()    

                    return (batchIndex, translatedLines)
                }
            }
            
            // --- CHANGE START: 修改结果收集逻辑 ---
            // 收集第一阶段的结果
            for await (batchIndex, translations) in group {
                let startIndex = batchIndex * batchSize
                let batch = batches[batchIndex]
                
                if translations.count == batch.count {
                    // 如果成功，解析并填充结果
                    for (i, translationText) in translations.enumerated() {
                        let resultIndex = startIndex + i
                        let translation = translationText.replacingOccurrences(of: #"^\[\d+\]\s*"#, with: "", options: .regularExpression).trimmingCharacters(in: .whitespaces)
                        intermediateResults[resultIndex].translated = translation
                    }
                } else {
                    // 如果失败，将整个批次标记为需要精校
                    for i in 0..<batch.count {
                        let resultIndex = startIndex + i
                        intermediateResults[resultIndex].needsRefinement = true
                    }
                }
            }
            // --- CHANGE END ---
        }
        
        // --- 阶段二：单行精校 ---
        print("\n--- 阶段二：开始单行精校 ---")
        let tasksToRefine = intermediateResults.enumerated().filter { $0.element.needsRefinement }
        
        if tasksToRefine.isEmpty {
            print("所有批次均初步成功，无需精校。")
            await MainActor.run { self.completedBatches = self.totalBatches } // 直接完成进度条
        } else {
            print("发现 \(tasksToRefine.count) 行字幕需要精校，开始处理...")
            
            await withTaskGroup(of: (index: Int, refinedText: String).self) { group in
                let taskSemaphore = AsyncSemaphore(value: maxConcurrent)
                
                for (index, task) in tasksToRefine {
                    await taskSemaphore.wait()
                    group.addTask {
                        // 跳过空行
                        if task.original.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                            await MainActor.run {
                                self.completedBatches += 1
                                self.updateProgress()
                            }
                            await taskSemaphore.signal()
                            return (index, "")
                        }
                        
                        // 为当前行构建滑动窗口上下文
                        let context = self.buildSlidingWindowContext(for: index, in: subtitles)
                        
                        // 构建高质量的单行精校Prompt
                        let messages = self.buildRefinementMessages(context: context, target: task.original, summary: summary, keywords: keywords)

                        
                        var refinedTranslation = "【精校失败】"
                        do {
                            let aiResponse = try await self.callAI(messages: messages, maxTokens: 1000)

                            print("单行校对结果：\(aiResponse)")
                            
                            // 处理AI响应，确保只取第一行
                            refinedTranslation = aiResponse
                                .trimmingCharacters(in: .whitespacesAndNewlines)
                                .components(separatedBy: .newlines) //只取第一行
                                .first ?? "【精校失败】"
                            
                            print("✅ 精校成功 - 原文: \(task.original)... -> 翻译: \(refinedTranslation)...")
                        } catch {
                            print("❌ 精校失败 - 原文: \(task.original.prefix(20))... Error: \(error)")
                        }
                        
                        await MainActor.run {
                            self.completedBatches += 1
                            self.updateProgress()
                        }

                        // 正确的代码 - 在这里添加 signal 调用
                        await taskSemaphore.signal()
                        
                        return (index, refinedTranslation)
                    }
                }
                
                // 将精校结果更新回主数组
                for await (index, refinedText) in group {
                    intermediateResults[index].translated = refinedText
                }
            }
        }
        
        // --- 最终处理 ---
        // 将中间结果转换为最终输出格式
        let finalResults = intermediateResults.map {
            (original: $0.original, translated: $0.translated, start: $0.start, end: $0.end)
        }
        
        print("\n翻译处理完成，总共翻译了 \(finalResults.count) 条字幕")
        return finalResults
    }

    // MARK: - 辅助函数 (Helper Functions)
    
    /// 为精校阶段构建滑动窗口上下文。
    private func buildSlidingWindowContext(for index: Int, in subtitles: [(text: String, start: Double, end: Double)], windowSize: Int = 10) -> String {
        let startIndex = max(0, index - windowSize)
        let endIndex = min(subtitles.count, index + windowSize + 1)
        
        let contextLines = subtitles[startIndex..<endIndex].enumerated().map { (i, subtitle) -> String in
            let actualIndex = startIndex + i
            if actualIndex == index {
                return ">> [TARGET] \(subtitle.text)" // 使用特殊标记清晰地指出目标行
            } else {
                return subtitle.text
            }
        }
        
        return contextLines.joined(separator: "\n")
    }

    /// 为单行精校构建请求消息体。
    private func buildRefinementMessages(context: String, target: String, summary: String, keywords: String) -> [[String: Any]] {
        let contextSummary = if !manualKeywords.isEmpty {
            "\(summary)\n关键词：\(manualKeywords)"
        } else {
            summary
        }
        
        // 从 context 中提取目标行（去掉 >> [TARGET] 标记）
        let targetLine = context.components(separatedBy: .newlines)
            .first { $0.contains(">> [TARGET]") }?
            .replacingOccurrences(of: ">> [TARGET] ", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines) ?? target

        print("targetline:\(targetLine)")
        
        return [
            ["role": "system", "content": """
            你是一名顶级的字幕翻译专家。你的任务是将指定的单行文本翻译成\(self.aiTargetLanguage)。
            
            重要规则：
            1. 只翻译指定的那一行，不要翻译上下文中的其他行
            2. 输出格式：仅输出翻译结果，不要任何前缀、标记、引号或解释
            3. 如果输出多行，只保留第一行作为结果
            4. 保持原文的语气和风格
            ---
            主题摘要: \(contextSummary)
            关键词: \(keywords)
            ---
            """],
            ["role": "user", "content": """
            上下文信息（仅供参考，不要翻译）：
            \(context)
            
            目标翻译行：\(targetLine)
            
            请只翻译上面这一行，输出格式：直接输出翻译结果，不要任何标记。
            """]
        ]
    }
    
    // 这个函数可以保留，用于批量翻译的Prompt构建
    private func buildBatchMessages(texts: [String], contextSummary: String, keywords: String) -> [[String: Any]] {
        return [
            ["role": "system", "content": """
            请根据主题摘要 \(contextSummary)和关键词 \(keywords)，并尊重原意的前提下，保持原有格式不变，用\(self.aiTargetLanguage)语言重写内容。

            警告：联系上下文，注意翻译的流畅度，自然度。
            不能出现这种错误：在几行字幕的中英文未能准确对齐，导致翻译结果不准确。

            注意：
            - 每行原文对应一行译文，禁止擅自做主合并原文进行重写
            - 只输出译文重写结果
            - 忠于原文顺序，但让\(self.aiTargetLanguage)语序自然，读起来顺畅，不拗口
            - 把重要概念（如：高人才密度、坦率沟通、自由与责任）准确传达，没有遗漏
            - 行尾适当断句，保持口语节奏感
            - 确保重写与主题相符，保持语言的信达雅
            - 保证关键词在前后文的一致性，统一人名
            - 保持每行开头的序号标记 [数字]
            - 如果是播客（podcast）内容的话，请自动省略like、you know、I mean等一些口语化词语的翻译，让翻译结果更符合播客的语境
            """],
            ["role": "user", "content": texts.joined(separator: "\n")]
        ]
    }
    
    // 异步信号量实现
    actor AsyncSemaphore {
        private var value: Int
        private var waiters: [CheckedContinuation<Void, Never>] = []
        
        init(value: Int) {
            self.value = value
        }
        
        func wait() async {
            if value > 0 {
                value -= 1
            } else {
                await withCheckedContinuation { continuation in
                    waiters.append(continuation)
                }
            }
        }
        
        func signal() {
            if !waiters.isEmpty {
                let waiter = waiters.removeFirst()
                waiter.resume()
            } else {
                value += 1
            }
        }
    }
    
    // 修改 callAI 函数，提高稳定性并增加日志
    private func callAI(messages: [[String: Any]], maxTokens: Int = 4096) async throws -> String {
        // 使用异步缓存检查
        let cachedResponse = await Task<String?, Never> {
            return AICacheManager.shared.getCachedResponse(for: messages, maxTokens: maxTokens)
        }.value
        
        if let cachedResponse = cachedResponse {
            return cachedResponse
        }
        
        print("未找到缓存，准备进行API请求")
        
        // 获取当前选择的服务商配置
        let provider = Config.shared.aiProvider
        let baseURL: String
        let model: String
        
        // 根据服务商设置 baseURL 和 model
        switch provider {
        case .deepseek:
            baseURL = "https://api.deepseek.com/v1/chat/completions"
            model = "deepseek-chat"
        case .volcengine:
            baseURL = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
            model = "deepseek-v3-241226"
        case .aliyun:
            baseURL = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
            model = "deepseek-v3"
        case .huaweiyun:
            baseURL = "https://api.siliconflow.cn/v1/chat/completions"
            model = "deepseek-ai/DeepSeek-V3"
       case .onerouter:
           baseURL = "https://openrouter.ai/api/v1/chat/completions"
           model = "deepseek/deepseek-chat-v3-0324:free"
        }
        
        // 检查 API Key
        guard let apiKey = Config.shared.apiKey, !apiKey.isEmpty else {
            throw NSError(domain: "AITranslation", 
                         code: -1, 
                         userInfo: [NSLocalizedDescriptionKey: "请先在设置中配置 API Key"])
        }
        
        // 构建请求 URL
        guard let url = URL(string: baseURL) else {
            print("❌ URL无效")
            throw NSError(domain: "AITranslation", code: -1, 
                         userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])
        }
        
        // 构建请求体
        let requestBody: [String: Any] = [
            "model": model,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": maxTokens,
            "stream": false
        ]
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        
        // 设置超时时间
        request.timeoutInterval = provider == .aliyun ? 120 : 60
        
        do {
            let requestData = try JSONSerialization.data(withJSONObject: requestBody)
            request.httpBody = requestData
            print("请求体大小: \(requestData.count) 字节")
        } catch {
            print("❌ 请求序列化失败: \(error)")
            throw error
        }
        
        // 增加超时时间到60秒
        // request.timeoutInterval = 60
        // request.timeoutInterval = 120//阿里云百炼需要120秒；
        // request.timeoutInterval = 60
        // print("设置请求超时时间: 60秒")
        
        // 添加重试机制
        let maxRetries = 3
        var retryCount = 0
        
        while retryCount < maxRetries {
            do {
                print("发送API请求 (尝试 \(retryCount+1)/\(maxRetries))")
                let startTime = Date()
                
                let (data, response) = try await URLSession.shared.data(for: request)
                let duration = Date().timeIntervalSince(startTime)
                
                print("请求完成，耗时: \(String(format: "%.2f", duration)) 秒")
                print("响应数据大小: \(data.count) 字节")
                
                // 检查数据是否为空
                guard !data.isEmpty else {
                    print("❌ 收到空数据响应")
                    retryCount += 1
                    try await Task.sleep(nanoseconds: 2_000_000_000)
                    continue
                }
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    print("❌ 响应类型无效")
                    throw NSError(domain: "AITranslation", code: -2, 
                                userInfo: [NSLocalizedDescriptionKey: "Invalid response type"])
                }
                
                print("HTTP状态码: \(httpResponse.statusCode)")
                
                if !(200...299).contains(httpResponse.statusCode) {
                    let errorString = String(data: data, encoding: .utf8) ?? "Unknown error"
                    print("❌ API错误响应: \(errorString)")
                    if errorString.contains("Api key is invalid") {
                        let alert = NSAlert()
                        alert.messageText = "API 错误"
                        alert.informativeText = "API 密钥无效，请在设置中检查并重新填写有效的 API Key。"
                        alert.addButton(withTitle: "确定")
                        alert.runModal()

                        // 直接抛出错误，终止重试
                        throw NSError(domain: "AITranslation", code: httpResponse.statusCode,
                                    userInfo: [NSLocalizedDescriptionKey: "API密钥无效"])
                    }
                    
                    // 429 频率限制
                    if httpResponse.statusCode == 429 {
                        print("请求频率限制，等待后重试")
                        retryCount += 1
                        try await Task.sleep(nanoseconds: 3_000_000_000)
                        continue
                    } else {
                        throw NSError(domain: "AITranslation", code: httpResponse.statusCode, 
                                    userInfo: [NSLocalizedDescriptionKey: "API错误: \(errorString)"])
                    }
                }
                
                // 解析响应
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let choices = json["choices"] as? [[String: Any]],
                   let firstChoice = choices.first,
                   let message = firstChoice["message"] as? [String: Any],
                   let content = message["content"] as? String {
                    
                    print("✅ 成功解析响应，内容长度: \(content.count)")
                    
                    // 缓存响应
                    AICacheManager.shared.cacheResponse(content, for: messages, maxTokens: maxTokens)
                    print("已缓存API响应")
                    
                    return content
                } else {
                    print("❌ JSON格式不符合预期")
                    // 尝试输出收到的JSON结构用于调试
                    if let jsonObj = try? JSONSerialization.jsonObject(with: data),
                       let jsonData = try? JSONSerialization.data(withJSONObject: jsonObj, options: .prettyPrinted),
                       let jsonStr = String(data: jsonData, encoding: .utf8) {
                        print("收到的JSON: \(jsonStr.prefix(200))...")
                    }
                    
                    throw NSError(domain: "AITranslation", code: -3, 
                                userInfo: [NSLocalizedDescriptionKey: "响应格式不符合预期"])
                }
                
            } catch {
                let nsError = error as NSError
                print("❌ API调用失败 (尝试 \(retryCount+1)/\(maxRetries)): \(error)")
                
                // 检查是否为API密钥无效错误，直接抛出终止重试
                if nsError.domain == "AITranslation", nsError.localizedDescription == "API密钥无效" {
                    throw error
                }

                if nsError.domain == NSURLErrorDomain {
                    if nsError.code == NSURLErrorTimedOut {
                        print("请求超时")
                    } else if nsError.code == NSURLErrorNotConnectedToInternet {
                        print("网络连接丢失")
                    } else {
                        print("URL错误代码: \(nsError.code)")
                    }
                }
                
                retryCount += 1
                if retryCount == maxRetries {
                    print("达到最大重试次数，放弃请求")
                    throw error
                }
                
                let waitTime: UInt64 = 2_000_000_000
                print("等待2秒后重试...")
                try await Task.sleep(nanoseconds: waitTime)
            }
        }
        
        print("所有API请求尝试均失败")
        throw NSError(domain: "AITranslation", code: -4, 
                     userInfo: [NSLocalizedDescriptionKey: "API调用达到最大重试次数后仍然失败"])
    }
    
    // 生成字幕行
    private func generateSubtitleLines(from segmentedText: String) -> [String] {
        // 分割文本并过滤空行
        let lines = segmentedText.components(separatedBy: "<br>")
            .filter { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
        
        // print("生成 \(lines.count) 行字幕，保留原始断句")
        
        // 直接返回分割后的行，不进行合并
        return lines
    }
    
    // 改进的分配时间戳函数，保留标点符号并修复时间戳问题
    private func assignTimestamps(to lines: [String]) -> [(text: String, start: Double, end: Double)] {
        // print("开始分配时间戳，共 \(lines.count) 行")
        var result: [(text: String, start: Double, end: Double)] = []
        
        // 1. 构建按时间顺序排列的单词列表
        var orderedWords: [(word: String, start: Double, end: Double, segmentIndex: Int)] = []
        var originalSegmentRanges: [(start: Double, end: Double, wordStartIndex: Int, wordEndIndex: Int)] = []
        
        for (segmentIndex, segment) in originalSegments.enumerated() {
            let wordStartIndex = orderedWords.count
            
            // 添加该segment中的所有单词
            orderedWords.append(contentsOf: segment.words.map {
                (word: $0.word.lowercased().trimmingCharacters(in: .punctuationCharacters),
                 start: $0.start,
                 end: $0.end,
                 segmentIndex: segmentIndex)
            })
            
            let wordEndIndex = orderedWords.count - 1
            
            // 记录segment的范围信息
            originalSegmentRanges.append((
                start: segment.startTime,
                end: segment.endTime,
                wordStartIndex: wordStartIndex,
                wordEndIndex: wordEndIndex
            ))
        }
        
        // 2. 创建快速查找索引
        var wordIndex: [String: [(start: Double, end: Double, globalIndex: Int, segmentIndex: Int)]] = [:]
        for (index, word) in orderedWords.enumerated() {
            let key = word.word
            if !key.isEmpty {
                if wordIndex[key] == nil {
                    wordIndex[key] = []
                }
                wordIndex[key]?.append((
                    start: word.start,
                    end: word.end,
                    globalIndex: index,
                    segmentIndex: word.segmentIndex
                ))
            }
        }
        
        // 3. 初始化跟踪变量
        var currentGlobalIndex = 0  // 这里添加了缺失的变量声明
        var lastEndTime = 0.0
        let maxAllowedTimeJump = 10.0  // 允许的最大时间跳跃（秒）
        
        // 处理每一行
        for (lineIndex, line) in lines.enumerated() {
            let searchWords = line.split(separator: " ")
                .map { $0.lowercased().trimmingCharacters(in: .punctuationCharacters) }
                .filter { !$0.isEmpty }
            
            if searchWords.isEmpty {
                // 处理空行
                let defaultDuration = 0.005
                let newEntry = (text: line, 
                              start: result.last?.end ?? 0.0,
                              end: (result.last?.end ?? 0.0) + defaultDuration)
                result.append(newEntry)
                continue
            }
            
            // print("处理字幕行 #\(lineIndex+1): \"\(line.prefix(30))...\"，包含 \(searchWords.count) 个单词")
            
            // 确定期望的时间范围
            let expectedStartTime = result.last?.end ?? 0.0
            lastEndTime = expectedStartTime
            
            // 4. 严格限制时间范围的匹配搜索
            var bestMatch: (start: Double, end: Double, confidence: Double, globalIndex: Int)? = nil
            var timeRangeMatches: [(start: Double, end: Double, confidence: Double, globalIndex: Int)] = []
            
            // 在全局单词列表中搜索，但严格限制时间范围
            for startIndex in currentGlobalIndex..<min(currentGlobalIndex + 200, orderedWords.count) {
                // 严格检查时间连续性 - 最重要的改进
                let currentWordTime = orderedWords[startIndex].start
                if abs(currentWordTime - expectedStartTime) > maxAllowedTimeJump {
                    continue // 直接跳过时间不连续的匹配
                }
                
                var matchCount = 0
                var currentIndex = startIndex
                var matchedIndices: [Int] = []
                
                // 尝试匹配当前行的单词
                for word in searchWords {
                    if currentIndex >= orderedWords.count {
                        break
                    }
                    
                    // 如果当前单词匹配
                    if orderedWords[currentIndex].word == word {
                        matchedIndices.append(currentIndex)
                        currentIndex += 1
                        matchCount += 1
                        continue
                    }
                    
                    // 向前查找几个单词（仍保持在合理时间范围内）
                    let lookAheadLimit = 3
                    var found = false
                    
                    for lookAhead in 1...min(lookAheadLimit, orderedWords.count - currentIndex - 1) {
                        let nextIndex = currentIndex + lookAhead
                        
                        // 确保不会有大的时间跳跃
                        if abs(orderedWords[nextIndex].start - orderedWords[currentIndex].start) > 5.0 {
                            break
                        }
                        
                        if orderedWords[nextIndex].word == word {
                            matchedIndices.append(nextIndex)
                            currentIndex = nextIndex + 1
                            matchCount += 1
                            found = true
                            break
                        }
                    }
                    
                    if !found {
                        break
                    }
                }
                
                // 计算匹配置信度和时间连续性
                if matchCount > 0 && !matchedIndices.isEmpty {
                    let confidence = Double(matchCount) / Double(searchWords.count)
                    let matchStart = orderedWords[matchedIndices.first!].start
                    let matchEnd = orderedWords[matchedIndices.last!].end
                    
                    // 时间连续性评分 - 非常重要
                    let timeContinuityScore = 1.0 - min(abs(matchStart - expectedStartTime) / maxAllowedTimeJump, 1.0)
                    
                    // 综合评分
                    let combinedScore = confidence * 0.5 + timeContinuityScore * 0.5
                    
                    // 只接受时间合理的匹配
                    if timeContinuityScore > 0.5 {
                        timeRangeMatches.append((
                            start: matchStart,
                            end: matchEnd,
                            confidence: combinedScore,
                            globalIndex: matchedIndices.last!
                        ))
                        
                        // 更新最佳匹配
                        if bestMatch == nil || combinedScore > bestMatch!.confidence {
                            bestMatch = timeRangeMatches.last
                        }
                    }
                }
            }
            
            // 5. 时间连续性强制执行
            if let match = bestMatch, abs(match.start - expectedStartTime) <= maxAllowedTimeJump {
                // 使用匹配的时间范围，但确保连续性
                let adjustedStart = max(match.start, expectedStartTime)
                let minimumDuration = max(0.5, Double(searchWords.count) * 0.3)
                let calculatedEnd = max(match.end, adjustedStart + minimumDuration)
                
                result.append((text: line, 
                             start: adjustedStart,
                             end: calculatedEnd))
                
                // 更新跟踪变量
                lastEndTime = calculatedEnd
                currentGlobalIndex = match.globalIndex + 1
                
                // print("✓ 行 #\(lineIndex+1) 匹配成功，置信度: \(String(format: "%.3f", match.confidence))，时间: \(String(format: "%.3f", adjustedStart))-\(String(format: "%.3f", calculatedEnd))")
            } else {
                // 找不到时间连续的匹配，使用估计时间
                let wordCount = searchWords.count
                let estimatedDuration = max(0.5, Double(wordCount) * 0.3)
                let newStart = lastEndTime
                let newEnd = newStart + estimatedDuration
                
                result.append((text: line,
                             start: newStart,
                             end: newEnd))
                
                lastEndTime = newEnd
                
                // print("⚠️ 行 #\(lineIndex+1) 匹配失败，使用估计时间: \(String(format: "%.3f", newStart))-\(String(format: "%.3f", newEnd))")
            }
        }
        
        // 6. 最终时间验证和调整
        return result.enumerated().map { index, element in
            var adjusted = element
            
            // 确保每个字幕行有足够的显示时间
            let minDuration = 0.5 // 最小0.5秒
            if adjusted.end - adjusted.start < minDuration {
                adjusted.end = adjusted.start + minDuration
            }
            
            // 确保结束时间不超过下一段的开始时间
            if index < result.count - 1 {
                adjusted.end = min(adjusted.end, result[index+1].start)
            }
            
            return adjusted
        }
    }
    
    // 时间格式化工具
    private func formatTimestamp(_ seconds: Double) -> String {
        let hours = Int(seconds) / 3600
        let minutes = (Int(seconds) % 3600) / 60
        let secs = Int(seconds) % 60
        let milliseconds = Int((seconds.truncatingRemainder(dividingBy: 1)) * 1000)
        
        return String(format: "%02d:%02d:%02d,%03d", hours, minutes, secs, milliseconds)
    }
    
    // 生成摘要的函数
    private func generateSummary(from subtitles: [(text: String, start: Double, end: Double)]) async throws -> String {
        let combinedText = subtitles.map { $0.text }.joined(separator: " ")
        
        let messages: [[String: Any]] = [
            ["role": "system", "content": """
            你是一个专业的内容总结专家。请对以下文本进行总结，用\(aiTargetLanguage)输出。要求：
            1. 总结不超过100字
            2. 保持原文的核心意思
            3. 使用简洁明了的语言
            4. 突出关键信息和主题
            5. 说明音视频风格（电影？电视剧？播客？访谈？演讲？综艺？）
            """],
            ["role": "user", "content": combinedText]
        ]
        
        return try await callAI(messages: messages, maxTokens: 2048)
    }
    
    // 生成关键词的函数
    private func generateKeywords(from subtitles: [(text: String, start: Double, end: Double)]) async throws -> String {
        let combinedText = subtitles.map { $0.text }.joined(separator: " ")
        
        let messages: [[String: Any]] = [
            ["role": "system", "content": """
            分析这段字幕，并总结出以下在内容中高频出现的词汇和术语（源语言和目标语言），以及统一前后字幕的关键点，用\(aiTargetLanguage)输出。
            请输入文本：
            """],
            ["role": "user", "content": combinedText]
        ]
        
        return try await callAI(messages: messages, maxTokens: 2048)
    }

    // 添加缓存相关的结构体
    struct AIResponse: Codable {
        let content: String
        let timestamp: Date
    }
    
    // 添加AI响应缓存管理器
    class AICacheManager {
        static let shared = AICacheManager()
        private let cacheDirectory: URL
        private let cacheExpiration: TimeInterval = 24 * 3 * 60 * 60 // 3天缓存过期
        
        private init() {
            let fileManager = FileManager.default
            cacheDirectory = fileManager.urls(for: .cachesDirectory, in: .userDomainMask)[0]
                .appendingPathComponent("AICache")
            
            try? fileManager.createDirectory(at: cacheDirectory, 
                                          withIntermediateDirectories: true)
        }
        
        private func cacheKey(for messages: [[String: Any]], maxTokens: Int) -> String {
            let data = try? JSONSerialization.data(withJSONObject: messages)
            let inputString = "\(String(data: data ?? Data(), encoding: .utf8) ?? "")\(maxTokens)"
            return inputString.md5
        }
        
        func getCachedResponse(for messages: [[String: Any]], maxTokens: Int) -> String? {
            let key = cacheKey(for: messages, maxTokens: maxTokens)
            let cacheFile = cacheDirectory.appendingPathComponent("\(key).json")
            
            guard let data = try? Data(contentsOf: cacheFile),
                  let response = try? JSONDecoder().decode(AIResponse.self, from: data),
                  Date().timeIntervalSince(response.timestamp) < cacheExpiration
            else {
                return nil
            }
            
            return response.content
        }
        
        func cacheResponse(_ content: String, for messages: [[String: Any]], maxTokens: Int) {
            let key = cacheKey(for: messages, maxTokens: maxTokens)
            let cacheFile = cacheDirectory.appendingPathComponent("\(key).json")
            
            let response = AIResponse(content: content, timestamp: Date())
            if let data = try? JSONEncoder().encode(response) {
                try? data.write(to: cacheFile)
            }
        }
    }
    
    // 添加字幕导出类型枚举
    enum SubtitleExportType {
        case bilingual
        case original
        case translation
    }
    
    // 添加导出函数
    private func exportSRT(type: SubtitleExportType) {
        var srtContent = ""
        for (index, segment) in optimizedSegments.enumerated() {
            // 序号
            srtContent += "\(index + 1)\n"
            // 时间戳
            srtContent += "\(formatTimestamp(segment.startTime)) --> \(formatTimestamp(segment.endTime))\n"
            
            // 根据导出类型添加内容
            switch type {
            case .bilingual:
                if let translation = segment.translatedText {
                    srtContent += "\(translation)\n"
                }
                srtContent += "\(segment.text)\n"
            case .original:
                srtContent += "\(segment.text)\n"
            case .translation:
                if let translation = segment.translatedText {
                    srtContent += "\(translation)\n"
                }
            }
            
            // 空行
            srtContent += "\n"
        }
        
        // 保存文件
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [.text]
        savePanel.nameFieldStringValue = "subtitles.srt"
        
        savePanel.begin { response in
            if response == .OK, let url = savePanel.url {
                do {
                    try srtContent.write(to: url, atomically: true, encoding: .utf8)
                } catch {
                    // print("保存字幕文件失败: \(error)")
                }
            }
        }
    }
    
    // 改进的分割函数，保留原始单词时间戳，但不再发送滚动通知或执行滚动
    private func splitSegmentAtIndex(_ wordIndex: Int, in segmentIndex: Int) {
        guard segmentIndex >= 0 && segmentIndex < optimizedSegments.count else { return }
        
        let segment = optimizedSegments[segmentIndex]
        let words = segment.words
        
        // 确保索引在有效范围内
        guard wordIndex > 0 && wordIndex < words.count else { return }
        
        // 分割单词数组
        let splitWords = Array(words[0..<wordIndex])
        let remainingWords = Array(words[wordIndex...])
        
        // 创建新的段落
        var firstSegment = EditableSegment(words: splitWords)
        var secondSegment = EditableSegment(words: remainingWords)
        
        // 更新翻译文本（如果有）
        if let translatedText = segment.translatedText {
            // 根据单词比例分割翻译文本
            let ratio = Double(splitWords.count) / Double(words.count)
            let splitIndex = translatedText.index(translatedText.startIndex, 
                offsetBy: Int(Double(translatedText.count) * ratio))
            
            firstSegment.translatedText = String(translatedText[..<splitIndex])
            secondSegment.translatedText = String(translatedText[splitIndex...])
        }
        
        // 更新段落列表
        optimizedSegments.remove(at: segmentIndex)
        optimizedSegments.insert(contentsOf: [firstSegment, secondSegment], at: segmentIndex)
        
        // 更新选中状态，但不发送通知或执行滚动
        // selectedSegmentId = secondSegment.id
        // 不直接设置选中状态，而是通过selectSegment函数
        selectSegment(secondSegment.id)
        
        // 可选：确保滚动位置不变
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.scrollProxy?.scrollTo(segmentIndex, anchor: .center)
        }
    }

    func selectSegment(_ id: UUID) {
        // 避免跳动的关键：检查是否与当前选中的ID相同
        if selectedSegmentId != id {
            // 获取当前选中段落的索引(用于保持滚动位置)
            let currentIndex = optimizedSegments.firstIndex { $0.id == selectedSegmentId }
            
            // 更新选中ID
            selectedSegmentId = id
            
            // 重置光标位置
            cursorPosition = nil
            
            // 可选：如果想确保不会因为选中而滚动，可以添加以下代码
            // 注意：这里使用了延迟执行，确保在ID更新后的渲染周期结束后执行
            if let idx = currentIndex {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                    self.scrollProxy?.scrollTo(idx, anchor: .center)
                }
            }
        }
    }
    
    // 添加合并段落的函数，不再发送滚动通知或执行滚动
    private func mergeSegments(at index: Int) {
        guard index >= 0 && index < optimizedSegments.count - 1 else { return }
        
        let currentSegment = optimizedSegments[index]
        let nextSegment = optimizedSegments[index + 1]
        
        // 合并单词
        let mergedWords = currentSegment.words + nextSegment.words
        var mergedSegment = EditableSegment(words: mergedWords)
        
        // 合并翻译文本（如果有）
        if let currentTranslation = currentSegment.translatedText,
           let nextTranslation = nextSegment.translatedText {
            mergedSegment.translatedText = currentTranslation + " " + nextTranslation
        } else {
            mergedSegment.translatedText = currentSegment.translatedText ?? nextSegment.translatedText
        }
        
        // 更新段落列表
        optimizedSegments[index] = mergedSegment
        optimizedSegments.remove(at: index + 1)
        
        // 更新选中状态，但不发送通知或执行滚动
        // selectedSegmentId = mergedSegment.id
        // 不直接设置选中状态，而是通过selectSegment函数
        selectSegment(mergedSegment.id)

        // 可选：确保滚动位置不变
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.scrollProxy?.scrollTo(index, anchor: .center)
        }
    }
    
    // 格式化最终字幕
    private func formatFinalSubtitles(_ subtitles: [(original: String, translated: String, start: Double, end: Double)]) -> [EditableSegment] {
        // print("开始格式化最终字幕，共 \(subtitles.count) 行")
        
        var segments: [EditableSegment] = []
        
        for (index, subtitle) in subtitles.enumerated() {
            // 确保时间戳之间没有重叠
            let start = subtitle.start
            let end = subtitle.end
            
            // 将原文分割成单词，并为每个单词创建 EditableWord
            let words = subtitle.original.split(separator: " ").enumerated().map { wordIndex, word in
                // 为每个单词分配均匀的时间戳
                let duration = end - start
                let wordCount = subtitle.original.split(separator: " ").count
                let wordDuration = wordCount > 0 ? duration / Double(wordCount) : duration
                
                let wordStart = start + (Double(wordIndex) * wordDuration)
                let wordEnd = min(wordStart + wordDuration, end) // 确保不超过片段结束时间
                
                return EditableWord(
                    word: String(word),
                    start: wordStart,
                    end: wordEnd,
                    probability: 1.0
                )
            }
            
            // 如果没有单词，创建一个占位符单词
            let finalWords = !words.isEmpty ? words : [
                EditableWord(
                    word: subtitle.original,
                    start: start,
                    end: end,
                    probability: 1.0
                )
            ]
            
            // 创建新的 EditableSegment
            var segment = EditableSegment(words: finalWords)
            segment.translatedText = subtitle.translated.isEmpty ? nil : subtitle.translated
            
            // 输出调试信息
            // print("字幕行 \(index+1): \(formatTimestamp(start)) --> \(formatTimestamp(end)) 包含 \(finalWords.count) 个单词")
            
            segments.append(segment)
        }
        
        // 检查是否有重叠
        for i in 1..<segments.count {
            var current = segments[i]
            let previous = segments[i-1]
            
            if current.startTime <= previous.endTime {
                // print("⚠️ 格式化后检测到重叠: 行 \(i) (\(formatTimestamp(current.startTime))) 与行 \(i-1) (\(formatTimestamp(previous.endTime)))")
                
                // 修复重叠问题: 当前行的开始时间 < 前一行的结束时间
                if previous.endTime > current.startTime {
                    // 创建新的单词数组，调整时间戳
                    let timeDiff = previous.endTime - current.startTime
                    let updatedWords = current.words.map { word in
                        return EditableWord(
                            word: word.word,
                            start: word.start + timeDiff,
                            end: word.end + timeDiff,
                            probability: word.probability
                        )
                    }
                    
                    // 创建更新后的段落并替换
                    var updatedSegment = EditableSegment(words: updatedWords)
                    updatedSegment.translatedText = current.translatedText
                    segments[i] = updatedSegment
                    
                    // print("✓ 已修复重叠: 行 \(i) 开始时间从 \(formatTimestamp(current.startTime)) 调整到 \(formatTimestamp(previous.endTime))")
                }
            }
        }
        
        return segments
    }
    
    // 添加更新进度的函数
    private func updateProgress() {
        progress = "处理进度: \(completedBatches)/\(totalBatches)"
    }

    // 添加将转录单词组织成字幕行的函数
    private func organizeWordsIntoSentences(_ segments: [EditableSegment]) -> [EditableSegment] {
        // print("开始组织字幕行，原始段落数: \(segments.count)")
        var result: [EditableSegment] = []
        
        // 定义句子结束标点
        let sentenceEndPunctuation = CharacterSet(charactersIn: ".!?。！？")
        
        // 1. 先把所有单词收集到数组
        var allWords: [(word: EditableWord, segmentIndex: Int)] = []
        for (segmentIndex, segment) in segments.enumerated() {
            for word in segment.words {
                allWords.append((word: word, segmentIndex: segmentIndex))
            }
        }
        
        // 2. 按时间顺序排序单词
        // 使用元组数组存储单词和其原始段落索引
        // 通过排序确保时间的顺序性
        allWords.sort { $0.word.start < $1.word.start }
        
        // 3. 句子组织时的时间处理
        var currentSentenceWords: [EditableWord] = []
        var currentSentenceStart: Double = 0
        var currentSentenceEnd: Double = 0
        
        // 准备 SRT 内容
        // var srtContent = ""
        // var srtIndex = 1
        
        for (index, wordInfo) in allWords.enumerated() {
            let word = wordInfo.word
            
            // 如果是第一个单词，记录句子开始时间
            if currentSentenceWords.isEmpty {
                currentSentenceStart = word.start
            }
            
            // 添加当前单词
            currentSentenceWords.append(word)
            currentSentenceEnd = word.end
            
            // 检查是否是句子结束
            let isLastWord = index == allWords.count - 1
            let isSentenceEnd = word.word.last.map { sentenceEndPunctuation.contains($0.unicodeScalars.first!) } ?? false
            
            if isSentenceEnd || isLastWord {
                // 创建新的句子段落
                let newSegment = EditableSegment(words: currentSentenceWords)
                result.append(newSegment)
                
                // 添加到 SRT 内容
            //    srtContent += "\(srtIndex)\n"
            //    srtContent += "\(formatTimestamp(currentSentenceStart)) --> \(formatTimestamp(currentSentenceEnd))\n"
            //    srtContent += "\(currentSentenceWords.map { $0.word }.joined(separator: " "))\n\n"
                
                // print("创建新句子: \(currentSentenceWords.map { $0.word }.joined(separator: " "))")
                // print("时间范围: \(formatTimestamp(currentSentenceStart)) --> \(formatTimestamp(currentSentenceEnd))")
                
                // 增加 SRT 索引
                // srtIndex += 1
                
                // 重置当前句子
                currentSentenceWords = []
                currentSentenceStart = 0
                currentSentenceEnd = 0
            }
        }
        
        // 保存 SRT 文件
        // do {
        //     let fileManager = FileManager.default
        //     let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        //     let srtFilePath = documentsPath.appendingPathComponent("organized_subtitles.srt")
            
        //     try srtContent.write(to: srtFilePath, atomically: true, encoding: .utf8)
        //     print("✅ SRT文件已保存到: \(srtFilePath.path)")
        // } catch {
        //     print("❌ 保存SRT文件失败: \(error)")
        // }
        
        // print("字幕行组织完成，共生成 \(result.count) 个句子")
        return result
    }

    // 添加完整的 setupPlayer 函数
    private func setupPlayer() {
        // print("开始设置播放器")
        
        // 先清理现有的观察者和定时器
        NotificationCenter.default.removeObserver(self)
        timer?.invalidate()
        timer = nil
        
        // 通过通知获取播放器实例
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("PlayerDidInitialize"),
            object: nil,
            queue: .main
        ) { notification in
            if let player = notification.object as? AVPlayer {
                self.player = player
                // print("播放器初始化成功，设置时间观察器")
                
                // 使用 AVPlayer 的周期性时间观察器替代 Timer
                let interval = CMTime(seconds: 0.1, preferredTimescale: 1000)
                let timeObserver = player.addPeriodicTimeObserver(
                    forInterval: interval,
                    queue: .main
                ) { time in
                    let newTime = CMTimeGetSeconds(time)
                    if self.currentTime != newTime {
                        self.currentTime = newTime
                        // print("播放器时间更新: \(newTime)")
                    }
                }
                
                // 保存时间观察器的引用（添加一个属性来存储）
                self.timeObserver = timeObserver
                
                // 添加播放结束通知
                NotificationCenter.default.addObserver(
                    forName: .AVPlayerItemDidPlayToEndTime,
                    object: player.currentItem,
                    queue: .main
                ) { _ in
                    // print("播放结束")
                    self.currentTime = 0
                    self.player?.seek(to: .zero)
                }
            }
        }
    }

    // 添加属性来存储时间观察器
    @State private var timeObserver: Any?

    // 新的设置播放器观察器函数
    private func setupPlayerObserver() {
        guard let currentPlayer = player else {
            print("错误：播放器未初始化")
            return
        }
        
        // 设置新的时间观察器
        let interval = CMTime(seconds: 0.1, preferredTimescale: 600)
        timeObserver = currentPlayer.addPeriodicTimeObserver(
            forInterval: interval,
            queue: .main
        ) { time in
            let newTime = CMTimeGetSeconds(time)
            self.currentTime = newTime
            
            if self.enableAutoScroll, let currentIndex = self.getCurrentSegmentIndex() {
                // 只有当索引变化时才滚动
                if self.lastScrolledIndex != currentIndex {
                    self.lastScrolledIndex = currentIndex
                    
                    // 如果是靠近列表顶部的元素，显示更多后续内容
                    if currentIndex < 3 {
                        self.scrollProxy?.scrollTo(0) // 滚动到列表最顶部
                    }
                    // 如果是列表中间的元素，只要确保可见即可
                    else {
                        self.scrollProxy?.scrollTo(currentIndex)
                    }
                    // 如果接近列表末尾，显示更多前面的内容
                    // 可以根据总数动态计算
                }
            }
        }
        
        // 获取媒体时长
        if let currentItem = currentPlayer.currentItem {
            duration = CMTimeGetSeconds(currentItem.duration)
        }
    }
    
    private func cleanup() {
        if let timeObserver = timeObserver, let player = player {
            player.removeTimeObserver(timeObserver)
            self.timeObserver = nil
        }
        timer?.invalidate()
        timer = nil
    }

    // 🔑 使用统一的专业功能工具类
    // 检查专业功能并运行
    private func checkProFeatureAndRun(action: @escaping () -> Void) {
        if LicenseManager.shared.isPro() {
            action()
        } else {
            ProFeatureHelper.shared.showProFeatureAlert()
        }
    }

    // 标记短句函数
    private func markShortSubtitles() {
        // 清空之前的标记
        shortSegmentIndices.removeAll()
        
        // 确保转录语言是英文
        guard transcriptionLanguage.lowercased().contains("en") else {
            let alert = NSAlert()
            alert.messageText = "不支持的语言"
            alert.informativeText = "断句纠错功能仅支持英文字幕。"
            alert.addButton(withTitle: "确定")
            alert.runModal()
            return
        }
        
        // 定义结束标点符号集合
        let endPunctuations = [".", "!", "?"]
        
        // 扫描所有字幕行
        for index in 0..<optimizedSegments.count {
            // 跳过第一行，因为需要检查上一行
            if index == 0 { continue }
            
            let currentSegment = optimizedSegments[index]
            let previousSegment = optimizedSegments[index - 1]
            
            // 获取当前行和上一行的文本
            let currentText = currentSegment.text.trimmingCharacters(in: .whitespacesAndNewlines)
            let previousText = previousSegment.text.trimmingCharacters(in: .whitespacesAndNewlines)
            
            // 检查上一行是否以结束标点结尾
            let previousEndsWithPunctuation = endPunctuations.contains { previousText.hasSuffix($0) }
            
            // 检查当前行是否以结束标点结尾且单词数少于6
            let currentEndsWithPunctuation = endPunctuations.contains { currentText.hasSuffix($0) }
            let wordCount = currentText.split(separator: " ").count
            
            // 标记需要合并的行：
            // 1. 上一行不以结束标点结尾
            // 2. 当前行以结束标点结尾
            // 3. 当前行单词数少于6
            if !previousEndsWithPunctuation && currentEndsWithPunctuation && wordCount < 6 {
                shortSegmentIndices.insert(index)
            }
        }
        
        // 显示统计信息
        let shortSegmentsCount = shortSegmentIndices.count
        if shortSegmentsCount > 0 {
            let alert = NSAlert()
            alert.messageText = "断句检查结果"
            alert.informativeText = "共发现 \(shortSegmentsCount) 个，需要与上一行合并的短句，这些短句已被标红，建议与上一行合并。"
            alert.addButton(withTitle: "确定")
            alert.runModal()
        } else {
            let alert = NSAlert()
            alert.messageText = "断句检查结果"
            alert.informativeText = "未发现需要合并的短句，断句质量良好。"
            alert.addButton(withTitle: "确定")
            alert.runModal()
        }
    }

    // MARK: - 建议导航功能

    /// 导航到上一个建议
    private func navigateToPreviousSuggestion() {
        if currentSuggestionIndex > 0 {
            currentSuggestionIndex -= 1
            scrollToCurrentSuggestion()
        }
    }

    /// 导航到下一个建议
    private func navigateToNextSuggestion() {
        if currentSuggestionIndex < totalSuggestions - 1 {
            currentSuggestionIndex += 1
            scrollToCurrentSuggestion()
        }
    }

    /// 滚动到当前建议位置
    private func scrollToCurrentSuggestion() {
        // 获取有建议的段落索引列表
        let segmentsWithSuggestions = getSegmentsWithSuggestions()

        // 确保当前索引在有效范围内
        guard currentSuggestionIndex >= 0 && currentSuggestionIndex < segmentsWithSuggestions.count else {
            return
        }

        // 获取实际的段落索引
        let actualSegmentIndex = segmentsWithSuggestions[currentSuggestionIndex]

        // 滚动到该段落
        if let scrollProxy = scrollProxy {
            withAnimation(.easeInOut(duration: 0.3)) {
                scrollProxy.scrollTo(actualSegmentIndex, anchor: .center)
            }
        }
    }

    /// 获取有建议的段落索引列表
    private func getSegmentsWithSuggestions() -> [Int] {
        var indices: [Int] = []
        for (index, segment) in optimizedSegments.enumerated() {
            if let suggestions = segment.correctionSuggestions, !suggestions.isEmpty {
                indices.append(index)
            }
        }
        return indices
    }

    /// 接受所有建议
    private func acceptAllSuggestions() {
        Task {
            await acceptAllSuggestionsAsync()
            await MainActor.run {
                withAnimation(.easeInOut(duration: 0.3)) {
                    showSuggestionNavigation = false
                }
            }
        }
    }

    /// 忽略所有建议
    private func ignoreAllSuggestions() {
        Task {
            await ignoreAllSuggestionsAsync()
            await MainActor.run {
                withAnimation(.easeInOut(duration: 0.3)) {
                    showSuggestionNavigation = false
                }
            }
        }
    }

    /// 接受所有建议的异步版本
    private func acceptAllSuggestionsAsync() async {
        await MainActor.run {
            for index in optimizedSegments.indices {
                if let suggestions = optimizedSegments[index].correctionSuggestions {
                    for suggestion in suggestions {
                        handleAcceptSuggestion(segmentId: optimizedSegments[index].id, suggestion: suggestion)
                    }
                }
            }
        }
    }

    /// 忽略所有建议的异步版本
    private func ignoreAllSuggestionsAsync() async {
        await MainActor.run {
            for index in optimizedSegments.indices {
                if let suggestions = optimizedSegments[index].correctionSuggestions {
                    optimizedSegments[index].correctionSuggestions = nil
                }
            }
        }
    }

    /// 更新建议导航状态
    private func updateSuggestionNavigation(suggestions: Int) {
        // 计算有建议的段落数量，而不是建议总数
        let segmentsWithSuggestions = getSegmentsWithSuggestions()
        totalSuggestions = segmentsWithSuggestions.count
        currentSuggestionIndex = 0

        withAnimation(.easeInOut(duration: 0.3)) {
            showSuggestionNavigation = totalSuggestions > 0
        }
    }

    // MARK: - 视频片段选择器相关函数

    /// 更新播放器时间
    private func updatePlayerTime(from timeString: String) {
        guard let player = player,
              let seconds = timeStringToSeconds(timeString) else { return }

        let time = CMTime(seconds: seconds, preferredTimescale: 600)
        player.seek(to: time)
    }

    /// 将时间字符串转换为秒数
    private func timeStringToSeconds(_ timeString: String) -> Double? {
        let components = timeString.split(separator: ":").compactMap { Double($0) }
        guard components.count == 3 else { return nil }

        let hours = components[0]
        let minutes = components[1]
        let seconds = components[2]

        return hours * 3600 + minutes * 60 + seconds
    }

    /// 导出视频片段
    private func exportVideoClip() {
        onExportVideoClip(clipStartTime, clipEndTime)
    }
}
