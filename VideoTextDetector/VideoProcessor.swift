import Foundation
import Vision
import AVFoundation
import AppKit

struct TextSegment: Identifiable {
    let id = UUID()
    let text: String
    let startTime: Double
    let endTime: Double
    let frames: [DetectedFrame]
    let textBounds: CGRect
    let confidence: VNConfidence
}

class VideoProcessor: ObservableObject {
    @Published var detectedFrames: [DetectedFrame] = []
    @Published var textSegments: [TextSegment] = []
    @Published var isProcessing = false
    @Published var progress: Int = 0
    @Published var searchResults: [TextSegment] = []
    
    // 字幕区域选择状态
    struct SubtitleRegion {
        var rect: CGRect = .zero
        var isSelected: Bool = false
    }

    @Published var subtitleRegion = SubtitleRegion()
    @Published var subtitleSegments: [EditableSegment] = []
    
    func processVideo(
        url: URL, 
        recognitionLevel: VNRequestTextRecognitionLevel = .accurate,
        progressHandler: ((Double) -> Void)? = nil
    ) async throws -> [DetectedFrame] {
        await MainActor.run {
            self.isProcessing = true
            self.textSegments = []
            self.progress = 0
        }
        
        var detectedFrames: [DetectedFrame] = []
        var currentSegments: [String: (startTime: Double, frames: [DetectedFrame], textBounds: CGRect, confidence: VNConfidence)] = [:]
        
        let asset = AVAsset(url: url)
        let duration = try await asset.load(.duration)
        let durationSeconds = CMTimeGetSeconds(duration)
        print("视频总时长: \(durationSeconds)秒")
        
        // 每秒采样的帧数
        let frameRate: Double = 2
        let totalFrames = Int(durationSeconds * frameRate)
        
        let generator = AVAssetImageGenerator(asset: asset)
        generator.appliesPreferredTrackTransform = true
        generator.requestedTimeToleranceBefore = .zero
        generator.requestedTimeToleranceAfter = .zero
        
        // 遍历整个视频时长
        for frameIndex in 0..<totalFrames {
            let timeInSeconds = Double(frameIndex) / frameRate
            let time = CMTime(seconds: timeInSeconds, preferredTimescale: 600)
            
            do {
                let cgImage = try generator.copyCGImage(at: time, actualTime: nil)
                let nsImage = NSImage(cgImage: cgImage, size: NSSize(width: cgImage.width, height: cgImage.height))
                
                // 执行文本检测
                if let (detectedText, textBounds, confidence) = try await detectText(in: nsImage, recognitionLevel: recognitionLevel) {
                    let frame = DetectedFrame(
                        image: nsImage,
                        detectedText: detectedText,
                        timestamp: timeInSeconds,
                        textBounds: textBounds,
                        confidence: confidence
                    )
                    detectedFrames.append(frame)
                    updateTextSegments(frame: frame, currentSegments: &currentSegments)
                }
            } catch {
                print("处理第 \(frameIndex) 帧时出错: \(error)")
                continue
            }
            
            // 更新进度
            let progress = Double(frameIndex + 1) / Double(totalFrames) * 100
            progressHandler?(progress) // 调用进度回调
        }
        
        // 处理完成后更新文本段
        var newSegments: [TextSegment] = []
        for (text, segmentInfo) in currentSegments {
            let cleanText = text.split(separator: "_").first.map(String.init) ?? text
            
            let textSegment = TextSegment(
                text: cleanText,
                startTime: segmentInfo.startTime,
                endTime: segmentInfo.frames.last?.timestamp ?? segmentInfo.startTime,
                frames: segmentInfo.frames,
                textBounds: segmentInfo.textBounds,
                confidence: segmentInfo.confidence
            )
            newSegments.append(textSegment)
        }
        
        // 按时间排序
        newSegments.sort { $0.startTime < $1.startTime }
        
        await MainActor.run {
            self.textSegments = newSegments
            self.isProcessing = false
            print("视频处理完成，共检测到 \(newSegments.count) 个文本段落")
        }
        
        return detectedFrames
    }
    
    private func detectText(in image: NSImage, recognitionLevel: VNRequestTextRecognitionLevel) async throws -> (String, CGRect, VNConfidence)? {
        guard let cgImage = image.cgImage(forProposedRect: nil, context: nil, hints: nil) else {
            return nil
        }
        
        return try await withCheckedThrowingContinuation { continuation in
            let request = VNRecognizeTextRequest { request, error in
                if let error = error {
                    continuation.resume(throwing: error)
                    return
                }
                
                guard let observations = request.results as? [VNRecognizedTextObservation] else {
                    continuation.resume(returning: nil)
                    return
                }
                
                var detectedText = ""
                var unionRect = CGRect.zero
                var isFirst = true
                var maxConfidence: VNConfidence = 0.0
                
                for observation in observations {
                    if let candidate = observation.topCandidates(1).first {
                        if !isFirst {
                            detectedText += " "
                        }
                        detectedText += candidate.string
                        
                        // 更新最高置信度
                        maxConfidence = max(maxConfidence, candidate.confidence)
                        
                        // 转换边界框坐标
                        let boundingBox = observation.boundingBox
                        // Vision 框架使用的是左下角为原点的坐标系统
                        let convertedRect = CGRect(
                            x: boundingBox.origin.x,
                            y: boundingBox.origin.y, // 保持原始 y 坐标
                            width: boundingBox.width,
                            height: boundingBox.height
                        )
                        
                        if isFirst {
                            unionRect = convertedRect
                        } else {
                            unionRect = unionRect.union(convertedRect)
                        }
                        
                        isFirst = false
                    }
                }
                
                if !detectedText.isEmpty {
                    continuation.resume(returning: (detectedText, unionRect, maxConfidence))
                } else {
                    continuation.resume(returning: nil)
                }
            }
            
            // 配置文本识别请求
            request.recognitionLevel = recognitionLevel
            request.recognitionLanguages = ["zh-Hans", "en-US"]
            request.usesLanguageCorrection = true
            request.minimumTextHeight = 0.01
            request.automaticallyDetectsLanguage = true
            
            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
            try? handler.perform([request])
        }
    }
    
    private func updateTextSegments(frame: DetectedFrame, currentSegments: inout [String: (startTime: Double, frames: [DetectedFrame], textBounds: CGRect, confidence: VNConfidence)]) {
        guard !frame.detectedText.isEmpty else { return }
        
        // 清理和标准化检测到的文本
        let cleanedText = frame.detectedText.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 检查是否是相同或相似的文本
        if let existingSegment = currentSegments.first(where: { key, value in
            // 使用更宽松的匹配条件，允许小的差异
            let existingText = String(key.split(separator: "_").first ?? "") // 转换为 String
            let similarity = calculateSimilarity(between: existingText, and: cleanedText)
            return similarity > 0.8 // 80% 相似度阈值
        })?.key {
            // 获取现有段落的信息
            if let existingValue = currentSegments[existingSegment] {
                if frame.timestamp - existingValue.frames.last!.timestamp < 2.0 {
                    // 创建更新后的值
                    var updatedValue = existingValue
                    updatedValue.frames.append(frame)
                    updatedValue.textBounds = existingValue.textBounds.union(frame.textBounds)
                    updatedValue.confidence = max(existingValue.confidence, frame.confidence)
                    
                    // 更新字典
                    currentSegments[existingSegment] = updatedValue
                } else {
                    // 如果时间间隔太大，创建新的段落
                    let newKey = "\(cleanedText)_\(frame.timestamp)"
                    currentSegments[newKey] = (
                        startTime: frame.timestamp,
                        frames: [frame],
                        textBounds: frame.textBounds,
                        confidence: frame.confidence
                    )
                }
            }
        } else {
            // 创建新的段落
            currentSegments[cleanedText] = (
                startTime: frame.timestamp,
                frames: [frame],
                textBounds: frame.textBounds,
                confidence: frame.confidence
            )
        }
    }
    
    // 计算文本相似度的辅助函数
    private func calculateSimilarity(between text1: String, and text2: String) -> Double {
        let set1 = Set(text1)
        let set2 = Set(text2)
        let intersection = set1.intersection(set2)
        let union = set1.union(set2)
        return Double(intersection.count) / Double(union.count)
    }
    
    // 添加边界框调整函数
    private func adjustBoundingBox(_ rect: CGRect, for imageSize: CGSize) -> CGRect {
        // Vision 框架返回的坐标是标准化的（0-1），需要转换为实际像素坐标
        let pixelRect = CGRect(
            x: rect.origin.x * imageSize.width,
            y: rect.origin.y * imageSize.height,
            width: rect.width * imageSize.width,
            height: rect.height * imageSize.height
        )
        
        // 应用额外的校正因子（可以根据实际效果微调）
        let correctionFactor: CGFloat = 1.05 // 稍微扩大边界框
        
        return CGRect(
            x: pixelRect.origin.x * correctionFactor,
            y: pixelRect.origin.y * correctionFactor,
            width: pixelRect.width * correctionFactor,
            height: pixelRect.height * correctionFactor
        )
    }
    
    // 更新搜索方法以支持文本段
    func searchKeyword(_ keyword: String) -> [TextSegment] {
        return textSegments.filter { $0.text.lowercased().contains(keyword.lowercased()) }
    }

    func countKeywords(keyword: String) -> Int {
        return textSegments.filter { $0.text.lowercased().contains(keyword.lowercased()) }.count
    }

    // 处理指定区域内的字幕
    func processSubtitleRegion(
        url: URL,
        region: CGRect,
        recognitionLevel: VNRequestTextRecognitionLevel = .accurate,
        progressHandler: ((Double) -> Void)? = nil
    ) async throws -> [EditableSegment] {
        await MainActor.run {
            self.isProcessing = true
            self.progress = 0
        }
        
        var subtitleSegments: [EditableSegment] = []
        var currentSegments: [String: (startTime: Double, frames: [DetectedFrame], textBounds: CGRect, confidence: VNConfidence, chineseText: String?, englishText: String?)] = [:]

        
        let asset = AVAsset(url: url)
        let duration = try await asset.load(.duration)
        let durationSeconds = CMTimeGetSeconds(duration)
        print("视频总时长: \(durationSeconds)秒")
        
        // 每秒采样的帧数 (可以调整采样率)
        let frameRate: Double = 2
        let totalFrames = Int(durationSeconds * frameRate)
        
        let generator = AVAssetImageGenerator(asset: asset)
        generator.appliesPreferredTrackTransform = true
        generator.requestedTimeToleranceBefore = .zero
        generator.requestedTimeToleranceAfter = .zero
        
        // 遍历整个视频时长
        for frameIndex in 0..<totalFrames {
            let timeInSeconds = Double(frameIndex) / frameRate
            let time = CMTime(seconds: timeInSeconds, preferredTimescale: 600)
            
            do {
                let cgImage = try generator.copyCGImage(at: time, actualTime: nil)
                let nsImage = NSImage(cgImage: cgImage, size: NSSize(width: cgImage.width, height: cgImage.height))
                
                // 先裁剪图像到选择的字幕区域
                guard let croppedImage = cropImage(nsImage, to: region) else {
                    continue
                }
                
                // 在裁剪后的图像上检测文本
                if let (detectedText, textBounds, confidence) = try await detectText(in: croppedImage, recognitionLevel: recognitionLevel) {
                    // 创建检测帧，注意：textBounds需要相对于原始图像进行调整
                    let adjustedTextBounds = CGRect(
                        x: region.origin.x + textBounds.origin.x * region.width,
                        y: region.origin.y + textBounds.origin.y * region.height,
                        width: textBounds.width * region.width,
                        height: textBounds.height * region.height
                    )
                    
                    // 分离中文和英文
                    let (chineseText, englishText) = separateChineseAndEnglish(detectedText)
                    
                    let frame = DetectedFrame(
                        image: nsImage,
                        detectedText: detectedText,
                        timestamp: timeInSeconds,
                        textBounds: adjustedTextBounds,
                        confidence: confidence
                    )
                    
                    // 使用修改后的updateTextSegments函数
                    updateTextSegments(frame: frame, 
                                      chineseText: chineseText, 
                                      englishText: englishText, 
                                      currentSegments: &currentSegments)
                }
            } catch {
                print("处理第 \(frameIndex) 帧时出错: \(error)")
                continue
            }
            
            // 更新进度
            let progress = Double(frameIndex + 1) / Double(totalFrames) * 100
            progressHandler?(progress) // 调用进度回调
        }
        
        // 处理完成后创建可编辑段落
        var newSegments: [EditableSegment] = []
        for (text, segmentInfo) in currentSegments {
            let cleanText = text.split(separator: "_").first.map(String.init) ?? text
            
            // 检查是否为CJK语言
            let isCJKLanguage = containsCJKCharacters(cleanText)
            
            // 构造格式化的文本：中文一行，英文一行
            var formattedText = cleanText
            if let chineseText = segmentInfo.chineseText, let englishText = segmentInfo.englishText {
                formattedText = "\(chineseText)\n\(englishText)"
            }
            
            let word = EditableWord(
                word: formattedText,
                start: segmentInfo.startTime,
                end: segmentInfo.frames.last?.timestamp ?? segmentInfo.startTime,
                probability: 1.0
            )
            
            newSegments.append(EditableSegment(words: [word], isCJKLanguage: isCJKLanguage))
        }
        
        // 按时间排序
        newSegments.sort { $0.startTime < $1.startTime }
        
        await MainActor.run {
            self.subtitleSegments = newSegments
            self.isProcessing = false
            print("字幕处理完成，共检测到 \(newSegments.count) 个字幕段落")
        }
        
        return newSegments
    }

    // 裁剪图像到指定区域
    private func cropImage(_ image: NSImage, to rect: CGRect) -> NSImage? {
        guard let cgImage = image.cgImage(forProposedRect: nil, context: nil, hints: nil) else {
            return nil
        }
        
        let imageSize = image.size
        
        // 将标准化坐标转换为像素坐标
        let x = rect.origin.x * imageSize.width
        let y = rect.origin.y * imageSize.height
        let width = rect.width * imageSize.width
        let height = rect.height * imageSize.height
        
        // 创建裁剪区域
        let cropRect = CGRect(x: x, y: y, width: width, height: height)
        
        // 裁剪图像
        guard let croppedCGImage = cgImage.cropping(to: cropRect) else {
            return nil
        }
        
        return NSImage(cgImage: croppedCGImage, size: NSSize(width: width, height: height))
    }

    // 创建字幕段落
    private func createSubtitleSegment(text: String, startTime: Double, endTime: Double) -> EditableSegment {
        // 检查是否为CJK语言
        let isCJKLanguage = containsCJKCharacters(text)
        
        let word = EditableWord(
            word: text,
            start: startTime,
            end: endTime,
            probability: 1.0
        )
        
        return EditableSegment(words: [word], isCJKLanguage: isCJKLanguage)
    }

    // 判断文本是否包含CJK字符
    private func containsCJKCharacters(_ text: String) -> Bool {
        let pattern = "\\p{Han}|\\p{Hiragana}|\\p{Katakana}|\\p{Hangul}"
        let regex = try? NSRegularExpression(pattern: pattern, options: [])
        let range = NSRange(location: 0, length: text.utf16.count)
        return regex?.firstMatch(in: text, options: [], range: range) != nil
    }

    // 分离中文和英文
    private func separateChineseAndEnglish(_ text: String) -> (String?, String?) {
        var chineseText: String? = nil
        var englishText: String? = nil
        
        // 中文字符正则表达式
        let chinesePattern = "[\\p{Han}\\p{InCJK_Symbols_and_Punctuation}]+"
        let chineseRegex = try? NSRegularExpression(pattern: chinesePattern, options: [])
        
        // 英文字符正则表达式 (包括标点符号和数字)
        let englishPattern = "[A-Za-z0-9\\s.,?!'\";:\\-()\\[\\]{}]+"
        let englishRegex = try? NSRegularExpression(pattern: englishPattern, options: [])
        
        // 提取中文
        if let regex = chineseRegex {
            let range = NSRange(location: 0, length: text.utf16.count)
            let matches = regex.matches(in: text, options: [], range: range)
            
            if !matches.isEmpty {
                var chineseChars = [String]()
                for match in matches {
                    if let range = Range(match.range, in: text) {
                        chineseChars.append(String(text[range]))
                    }
                }
                chineseText = chineseChars.joined(separator: "")
            }
        }
        
        // 提取英文
        if let regex = englishRegex {
            let range = NSRange(location: 0, length: text.utf16.count)
            let matches = regex.matches(in: text, options: [], range: range)
            
            if !matches.isEmpty {
                var englishWords = [String]()
                for match in matches {
                    if let range = Range(match.range, in: text) {
                        englishWords.append(String(text[range]))
                    }
                }
                englishText = englishWords.joined(separator: " ").trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }
        
        return (chineseText, englishText)
    }

    // 更新文本段落，添加中英文分离支持
    private func updateTextSegments(
        frame: DetectedFrame, 
        chineseText: String?, 
        englishText: String?,
        currentSegments: inout [String: (startTime: Double, frames: [DetectedFrame], textBounds: CGRect, confidence: VNConfidence, chineseText: String?, englishText: String?)]
    ) {
        guard !frame.detectedText.isEmpty else { return }
        
        // 清理和标准化检测到的文本
        let cleanedText = frame.detectedText.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 检查是否是相同或相似的文本
        if let existingSegment = currentSegments.first(where: { key, value in
            // 使用更宽松的匹配条件，允许小的差异
            let existingText = String(key.split(separator: "_").first ?? "") // 转换为 String
            let similarity = calculateSimilarity(between: existingText, and: cleanedText)
            return similarity > 0.8 // 80% 相似度阈值
        })?.key {
            // 获取现有段落的信息
            if let existingValue = currentSegments[existingSegment] {
                if frame.timestamp - existingValue.frames.last!.timestamp < 2.0 {
                    // 创建更新后的值
                    var updatedValue = existingValue
                    updatedValue.frames.append(frame)
                    updatedValue.textBounds = existingValue.textBounds.union(frame.textBounds)
                    updatedValue.confidence = max(existingValue.confidence, frame.confidence)
                    
                    // 更新中英文文本（如果有新的）
                    if let newChineseText = chineseText, !newChineseText.isEmpty {
                        updatedValue.chineseText = newChineseText
                    }
                    if let newEnglishText = englishText, !newEnglishText.isEmpty {
                        updatedValue.englishText = newEnglishText
                    }
                    
                    // 更新字典
                    currentSegments[existingSegment] = updatedValue
                } else {
                    // 如果时间间隔太大，创建新的段落
                    let newKey = "\(cleanedText)_\(frame.timestamp)"
                    currentSegments[newKey] = (
                        startTime: frame.timestamp,
                        frames: [frame],
                        textBounds: frame.textBounds,
                        confidence: frame.confidence,
                        chineseText: chineseText,
                        englishText: englishText
                    )
                }
            }
        } else {
            // 创建新的段落
            currentSegments[cleanedText] = (
                startTime: frame.timestamp,
                frames: [frame],
                textBounds: frame.textBounds,
                confidence: frame.confidence,
                chineseText: chineseText,
                englishText: englishText
            )
        }
    }

    // 导出字幕为SRT格式
    func exportSubtitlesToSRT() -> String {
        var srtContent = ""
        
        for (index, segment) in subtitleSegments.enumerated() {
            srtContent += "\(index + 1)\n"
            srtContent += "\(formatTimeForSRT(seconds: segment.startTime)) --> \(formatTimeForSRT(seconds: segment.endTime))\n"
            srtContent += "\(segment.text)\n\n"
        }
        
        return srtContent
    }

    // 格式化时间为SRT格式
    private func formatTimeForSRT(seconds: Double) -> String {
        let hours = Int(seconds) / 3600
        let minutes = (Int(seconds) % 3600) / 60
        let secs = Int(seconds) % 60
        let milliseconds = Int((seconds - Double(Int(seconds))) * 1000)
        
        return String(format: "%02d:%02d:%02d,%03d", hours, minutes, secs, milliseconds)
    }

    // ========== 新增：分块高效硬字幕提取主流程 ==========
    func extractHardSubtitlesBlockwise(
        url: URL,
        blockDuration: Double = 120.0,
        frameRate: Double = 2.0,
        region: CGRect? = nil, // 新增参数
        recognitionLevel: VNRequestTextRecognitionLevel = .accurate,
        progressHandler: ((Double) -> Void)? = nil,
        completion: @escaping ([EditableSegment]) -> Void
    ) {
        Task {
            let asset = AVAsset(url: url)
            let duration = try await asset.load(.duration)
            let durationSeconds = CMTimeGetSeconds(duration)
            let totalBlocks = Int(ceil(durationSeconds / blockDuration))
            var currentSegments: [String: (startTime: Double, endTime: Double, chineseText: String?, englishText: String?)] = [:]
            var lastOcrText: String = ""
            var lastFingerprint: String = ""
            for blockIndex in 0..<totalBlocks {
                autoreleasepool {
                    self.processBlockWithMerge(
                        blockIndex: blockIndex,
                        asset: asset,
                        durationSeconds: durationSeconds,
                        blockDuration: blockDuration,
                        frameRate: frameRate,
                        region: region,
                        recognitionLevel: recognitionLevel,
                        lastFingerprint: &lastFingerprint,
                        lastOcrText: &lastOcrText,
                        currentSegments: &currentSegments,
                        progressHandler: progressHandler
                    )
                }
            }
            // 生成最终 EditableSegment
            var segments: [EditableSegment] = []
            for (text, seg) in currentSegments {
                let isCJK = containsCJKCharacters(text)
                var formattedText = text
                if let c = seg.chineseText, let e = seg.englishText, !c.isEmpty, !e.isEmpty {
                    formattedText = "\(c)\n\(e)"
                }
                let word = EditableWord(word: formattedText, start: seg.startTime, end: seg.endTime, probability: 1.0)
                segments.append(EditableSegment(words: [word], isCJKLanguage: isCJK))
            }
            // 按时间排序
            segments.sort { $0.startTime < $1.startTime }
            completion(segments)
        }
    }

    // 检测底部区域是否有字幕（以白色像素比例为例）
    private func frameHasSubtitle(_ image: NSImage, whiteThreshold: CGFloat = 0.85, minWhiteRatio: CGFloat = 0.08) -> Bool {
        guard let cgImage = image.cgImage(forProposedRect: nil, context: nil, hints: nil) else { return false }
        let width = cgImage.width
        let height = cgImage.height
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8
        guard let context = CGContext(data: nil, width: width, height: height, bitsPerComponent: bitsPerComponent, bytesPerRow: bytesPerRow, space: CGColorSpaceCreateDeviceRGB(), bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue) else { return false }
        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
        guard let data = context.data else { return false }
        let buffer = data.bindMemory(to: UInt8.self, capacity: width * height * bytesPerPixel)
        var whiteCount = 0
        for y in 0..<height {
            for x in 0..<width {
                let offset = y * bytesPerRow + x * bytesPerPixel
                let r = CGFloat(buffer[offset]) / 255.0
                let g = CGFloat(buffer[offset+1]) / 255.0
                let b = CGFloat(buffer[offset+2]) / 255.0
                let a = CGFloat(buffer[offset+3]) / 255.0
                if a > 0.7 && r > whiteThreshold && g > whiteThreshold && b > whiteThreshold {
                    whiteCount += 1
                }
            }
        }
        let total = width * height
        let ratio = CGFloat(whiteCount) / CGFloat(total)
        return ratio > minWhiteRatio
    }

    private func processBlockWithMerge(
        blockIndex: Int,
        asset: AVAsset,
        durationSeconds: Double,
        blockDuration: Double,
        frameRate: Double,
        region: CGRect?,
        recognitionLevel: VNRequestTextRecognitionLevel,
        lastFingerprint: inout String,
        lastOcrText: inout String,
        currentSegments: inout [String: (startTime: Double, endTime: Double, chineseText: String?, englishText: String?)],
        progressHandler: ((Double) -> Void)?
    ) {
        let blockStart = Double(blockIndex) * blockDuration
        let blockEnd = min(blockStart + blockDuration, durationSeconds)
        let blockFrameCount = Int((blockEnd - blockStart) * frameRate)
        let generator = AVAssetImageGenerator(asset: asset)
        generator.appliesPreferredTrackTransform = true
        generator.requestedTimeToleranceBefore = .zero
        generator.requestedTimeToleranceAfter = .zero
        var lastText = ""
        var lastStartTime: Double? = nil
        var lastChineseText: String? = nil
        var lastEnglishText: String? = nil
        for frameIdx in 0..<blockFrameCount {
            autoreleasepool {
                let timeInSeconds = blockStart + Double(frameIdx) / frameRate
                let time = CMTime(seconds: timeInSeconds, preferredTimescale: 600)
                var actualTime = CMTime.zero
                do {
                    let cgImage = try generator.copyCGImage(at: time, actualTime: &actualTime)
                    let realTimeInSeconds = CMTimeGetSeconds(actualTime)
                    let nsImage = NSImage(cgImage: cgImage, size: NSSize(width: cgImage.width, height: cgImage.height))
                    let useRegion = region ?? CGRect(x: 0, y: 0.8, width: 1.0, height: 0.2)
                    guard let croppedImage = cropImage(nsImage, to: useRegion) else { return }
                    // 新增：先检测是否有字幕
                    if !frameHasSubtitle(croppedImage) {
                        let progress = (Double(blockIndex) * blockDuration + Double(frameIdx) / frameRate) / durationSeconds * 100
                        progressHandler?(progress)
                        return
                    }
                    let fingerprint = imageFingerprint(croppedImage)
                    if fingerprint == lastFingerprint {
                        // 内容没变，跳过
                        let progress = (Double(blockIndex) * blockDuration + Double(frameIdx) / frameRate) / durationSeconds * 100
                        progressHandler?(progress)
                        return
                    }
                    lastFingerprint = fingerprint
                    if let (ocrText, _, _) = try? detectTextSync(in: croppedImage, recognitionLevel: recognitionLevel) {
                        let cleanText = ocrText.trimmingCharacters(in: .whitespacesAndNewlines)
                        let (chineseText, englishText) = separateChineseAndEnglish(cleanText)
                        if cleanText != lastText {
                            // 记录上一条字幕
                            if let start = lastStartTime, !lastText.isEmpty {
                                let key = lastText
                                currentSegments[key] = (startTime: start, endTime: realTimeInSeconds, chineseText: lastChineseText, englishText: lastEnglishText)
                            }
                            // 新字幕
                            lastText = cleanText
                            lastStartTime = realTimeInSeconds
                            lastChineseText = chineseText
                            lastEnglishText = englishText
                        }
                    }
                } catch {
                    #if DEBUG
                    print("处理第 \(frameIdx) 帧时出错: \(error)")
                    #endif
                    return
                }
                let progress = (Double(blockIndex) * blockDuration + Double(frameIdx) / frameRate) / durationSeconds * 100
                progressHandler?(progress)
            }
        }
        // 处理最后一条字幕
        if let start = lastStartTime, !lastText.isEmpty {
            let key = lastText
            currentSegments[key] = (startTime: start, endTime: blockEnd, chineseText: lastChineseText, englishText: lastEnglishText)
        }
    }

    private func processFrame(
        frameIdx: Int,
        blockStart: Double,
        frameRate: Double,
        generator: AVAssetImageGenerator,
        subtitleRegionHeightRatio: CGFloat,
        recognitionLevel: VNRequestTextRecognitionLevel,
        lastFingerprint: inout String,
        lastOcrText: inout String,
        allSegments: inout [EditableSegment],
        durationSeconds: Double,
        blockIndex: Int,
        blockDuration: Double,
        progressHandler: ((Double) -> Void)?
    ) {
        let timeInSeconds = blockStart + Double(frameIdx) / frameRate
        let time = CMTime(seconds: timeInSeconds, preferredTimescale: 600)
        do {
            let cgImage = try generator.copyCGImage(at: time, actualTime: nil)
            let nsImage = NSImage(cgImage: cgImage, size: NSSize(width: cgImage.width, height: cgImage.height))
            let region = CGRect(x: 0, y: 1.0 - subtitleRegionHeightRatio, width: 1.0, height: subtitleRegionHeightRatio)
            guard let croppedImage = cropImage(nsImage, to: region) else { return }
            let fingerprint = imageFingerprint(croppedImage)
            if fingerprint == lastFingerprint { return }
            lastFingerprint = fingerprint
            if let (ocrText, _, _) = try? detectTextSync(in: croppedImage, recognitionLevel: recognitionLevel) {
                let cleanText = ocrText.trimmingCharacters(in: .whitespacesAndNewlines)
                if !cleanText.isEmpty && cleanText != lastOcrText {
                    let word = EditableWord(word: cleanText, start: timeInSeconds, end: timeInSeconds + 1.0 / frameRate, probability: 1.0)
                    let isCJK = containsCJKCharacters(cleanText)
                    let segment = EditableSegment(words: [word], isCJKLanguage: isCJK)
                    allSegments.append(segment)
                    lastOcrText = cleanText
                }
            }
        } catch {
            #if DEBUG
            print("处理第 \(frameIdx) 帧时出错: \(error)")
            #endif
            return
        }
        let progress = (Double(blockIndex) * blockDuration + Double(frameIdx) / frameRate) / durationSeconds * 100
        progressHandler?(progress)
    }

    // 新增同步OCR辅助方法（假设detectText原本是async的，这里用try? await）
    private func detectTextSync(in image: NSImage, recognitionLevel: VNRequestTextRecognitionLevel) throws -> (String, CGRect, VNConfidence)? {
        var result: (String, CGRect, VNConfidence)? = nil
        let semaphore = DispatchSemaphore(value: 0)
        Task {
            result = try? await detectText(in: image, recognitionLevel: recognitionLevel)
            semaphore.signal()
        }
        semaphore.wait()
        return result
    }
    
    // ========== 新增：简单图像指纹算法 ==========
    private func imageFingerprint(_ image: NSImage) -> String {
        guard let cgImage = image.cgImage(forProposedRect: nil, context: nil, hints: nil) else { return "" }
        guard let context = CGContext(data: nil, width: 8, height: 8, bitsPerComponent: 8, bytesPerRow: 8, space: CGColorSpaceCreateDeviceGray(), bitmapInfo: 0) else { return "" }
        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: 8, height: 8))
        guard let data = context.data else { return "" }
        let buffer = data.bindMemory(to: UInt8.self, capacity: 64)
        let avg = (0..<64).map { Int(buffer[$0]) }.reduce(0, +) / 64
        let hash = (0..<64).map { Int(buffer[$0]) > avg ? "1" : "0" }.joined()
        return hash
    }
}

struct DetectedFrame: Identifiable {
    let id = UUID()
    let image: NSImage
    let detectedText: String
    let timestamp: Double
    let textBounds: CGRect
    let confidence: VNConfidence
}
