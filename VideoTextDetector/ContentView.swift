import SwiftUI
import WhisperKit
import AVFoundation
import Vision
import CoreML
import AVKit
import Translation

// MARK: - 类型定义
enum MediaType {
    case video
    case audio
}

// MARK: - 可折叠侧边导航栏组件
struct CollapsibleSidebar: View {
    @Binding var selectedSegment: Int
    @State private var isExpanded: Bool = false

    var body: some View {
        HStack(spacing: 0) {
            // 侧边栏
            VStack(spacing: 0) {
                // 顶部控制区域
                HStack {
                    if isExpanded {
                        Text("功能")
                            .font(.headline)
                            .foregroundColor(.primary)
                        Spacer()
                    }

                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isExpanded.toggle()
                        }
                    }) {
                        Image(systemName: isExpanded ? "sidebar.left" : "sidebar.right")
                            .font(.system(size: 16))
                            .foregroundColor(.secondary)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .help(isExpanded ? "收起侧边栏" : "展开侧边栏")
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 12)
                .background(Color(NSColor.controlBackgroundColor).opacity(0.8))

                // 导航按钮
                VStack(spacing: 4) {
                    SidebarButton(
                        icon: "wand.and.stars",
                        title: "AI断句/翻译优化",
                        shortTitle: "AI优化",
                        isSelected: selectedSegment == 0,
                        isExpanded: isExpanded,
                        action: { selectedSegment = 0 }
                    )

                    SidebarButton(
                        icon: "text.viewfinder",
                        title: "检测视频文本",
                        shortTitle: "文本检测",
                        isSelected: selectedSegment == 1,
                        isExpanded: isExpanded,
                        action: { selectedSegment = 1 }
                    )

                    SidebarButton(
                        icon: "text.bubble",
                        title: "提取硬字幕",
                        shortTitle: "硬字幕",
                        isSelected: selectedSegment == 2,
                        isExpanded: isExpanded,
                        action: { selectedSegment = 2 }
                    )
                }
                .padding(.horizontal, 8)
                .padding(.top, 8)

                Spacer()
            }
            .frame(width: isExpanded ? 180 : 50)
            .background(Color(NSColor.controlBackgroundColor))
            .overlay(
                Rectangle()
                    .frame(width: 1)
                    .foregroundColor(Color.gray.opacity(0.3)),
                alignment: .trailing
            )
        }
    }
}



// MARK: - 侧边栏按钮组件
struct SidebarButton: View {
    let icon: String
    let title: String
    let shortTitle: String
    let isSelected: Bool
    let isExpanded: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 16))
                    .frame(width: 20)

                if isExpanded {
                    Text(shortTitle)
                        .font(.system(size: 13))
                        .lineLimit(1)
                        .truncationMode(.tail)
                    Spacer()
                }
            }
            .padding(.horizontal, isExpanded ? 12 : 8)
            .padding(.vertical, 8)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .fill(isSelected ? Color.accentColor.opacity(0.2) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .foregroundColor(isSelected ? .accentColor : .primary)
        .help(title) // 鼠标悬停时显示完整标题
    }
}

// MARK: - 专业功能工具类
class ProFeatureHelper {
    static let shared = ProFeatureHelper()

    private init() {}

    /// 判断是否为专业模型
    func isProModel(_ modelName: String) -> Bool {
        return modelName.contains("large")
    }

    /// 显示专业功能提示
    func showProFeatureAlert(closeTranscriptionSettings: (() -> Void)? = nil) {
        // 🔑 使用DispatchQueue.main.async避免在SwiftUI事务中运行模态对话框
        DispatchQueue.main.async {
            let alert = NSAlert()
            alert.messageText = "需要专业版"
            alert.informativeText = "此功能需要购买专业版或终身会员才能使用。"

            // 添加按钮，注意顺序会影响显示位置
            alert.addButton(withTitle: "购买专业版")  // 主要操作
            alert.addButton(withTitle: "关闭")  // 取消操作

            let response = alert.runModal()

            switch response {
            case .alertFirstButtonReturn:  // 购买专业版
                // 🔑 关闭对话框，关闭转录设置页面，打开购买页面
                self.showPurchasePage(closeTranscriptionSettings: closeTranscriptionSettings)

            case .alertSecondButtonReturn:  // 关闭
                // 不需要额外处理，对话框会自动关闭
                break

            default:
                break
            }
        }
    }

    /// 直接显示购买页面（不显示提示对话框）
    func showPurchasePage(closeTranscriptionSettings: (() -> Void)? = nil) {
        // 🎨 先显示购买页面，创造平滑的视觉过渡
        var storeViewController: NSHostingController<StoreView>? = nil
        storeViewController = NSHostingController(rootView: StoreView(onClose: {
            storeViewController?.dismiss(nil as Any?)
        }))
        storeViewController?.preferredContentSize = NSSize(width: 650, height: 540)
        if let window = NSApp.mainWindow {
            window.contentViewController?.presentAsSheet(storeViewController!)
        }

        // 🎨 延迟关闭转录设置页面，让购买页面先显示
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            closeTranscriptionSettings?()
        }
    }
}

// 在 ContentView 结构体外部添加枚举定义
enum SubtitleExportType {
    case bilingual   // 双语字幕
    case original    // 原文字幕
    case translation // 翻译字幕
}

// 添加新的缓存结构体
struct SubtitleCache: Codable {
    let segments: [EditableSegment]
    let timestamp: Date
    let mediaPath: String // 用于区分不同的媒体文件
}

// 添加字幕缓存管理器
class SubtitleCacheManager {
    static let shared = SubtitleCacheManager()
    private let cacheDirectory: URL
    private let cacheExpiration: TimeInterval = 24 * 3 * 60 * 60 // 3天缓存过期
    
    private init() {
        let fileManager = FileManager.default
        cacheDirectory = fileManager.urls(for: .cachesDirectory, in: .userDomainMask)[0]
            .appendingPathComponent("SubtitleCache")
        
        try? fileManager.createDirectory(at: cacheDirectory, 
                                      withIntermediateDirectories: true)
    }
    
    private func cacheKey(for mediaURL: URL) -> String {
        // 使用媒体文件路径的MD5作为缓存键
        return mediaURL.path.md5
    }
    
    func getCachedSubtitles(for mediaURL: URL) -> [EditableSegment]? {
        let key = cacheKey(for: mediaURL)
        let cacheFile = cacheDirectory.appendingPathComponent("\(key).json")
        
        guard let data = try? Data(contentsOf: cacheFile),
              let cache = try? JSONDecoder().decode(SubtitleCache.self, from: data),
              Date().timeIntervalSince(cache.timestamp) < cacheExpiration,
              cache.mediaPath == mediaURL.path // 确保是同一个媒体文件
        else {
            return nil
        }
        
        return cache.segments
    }
    
    func cacheSubtitles(_ segments: [EditableSegment], for mediaURL: URL) {
        let key = cacheKey(for: mediaURL)
        let cacheFile = cacheDirectory.appendingPathComponent("\(key).json")

        // print("缓存文件路径: \(cacheFile.path)") // 添加调试信息
        
        let cache = SubtitleCache(
            segments: segments,
            timestamp: Date(),
            mediaPath: mediaURL.path
        )
        
        if let data = try? JSONEncoder().encode(cache) {
            // print("开始缓存字幕")
            try? data.write(to: cacheFile)
            // print("缓存字幕到: \(cacheFile.path)")
        } else {
            print("缓存字幕失败")
        }
    }
    
    // 添加清理过期缓存的方法
    func cleanExpiredCache() {
        let fileManager = FileManager.default
        guard let files = try? fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil) else {
            return
        }
        
        let now = Date()
        for file in files {
            guard let data = try? Data(contentsOf: file),
                  let cache = try? JSONDecoder().decode(SubtitleCache.self, from: data) else {
                continue
            }
            
            if now.timeIntervalSince(cache.timestamp) > cacheExpiration {
                try? fileManager.removeItem(at: file)
            }
        }
    }
}

class PlayerManager: ObservableObject {
    @Published var player: AVPlayer?
    @Published var currentTime: Double = 0
    @Published var duration: Double = 0
    @Published var isPlaying: Bool = false
    
    private var timeObserver: Any?
    
    func setupPlayer(with url: URL) {
        // 清理旧的播放器
        cleanup()
        
        // 创建新的播放器
        player = AVPlayer(url: url)
        
        // 设置时间观察器
        setupTimeObserver()
        
        // 获取媒体时长
        let asset = AVAsset(url: url)
        duration = CMTimeGetSeconds(asset.duration)
        
        // 添加播放结束通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(playerDidFinishPlaying),
            name: .AVPlayerItemDidPlayToEndTime,
            object: player?.currentItem
        )
    }
    
    private func setupTimeObserver() {
        guard let player = player else { return }
        
        let interval = CMTime(seconds: 0.1, preferredTimescale: 1000)
        timeObserver = player.addPeriodicTimeObserver(
            forInterval: interval,
            queue: .main
        ) { [weak self] time in
            self?.currentTime = CMTimeGetSeconds(time)
            self?.isPlaying = player.rate != 0
        }
    }
    
    @objc private func playerDidFinishPlaying() {
        isPlaying = false
        currentTime = 0
        player?.seek(to: .zero)
    }
    
    func cleanup() {
        if let timeObserver = timeObserver, let player = player {
            player.removeTimeObserver(timeObserver)
        }
        timeObserver = nil
        player = nil
        currentTime = 0
        duration = 0
        isPlaying = false
        
        // 移除通知观察者
        NotificationCenter.default.removeObserver(self)
    }
    
    // 播放控制方法
    func play() {
        player?.play()
        isPlaying = true
    }
    
    func pause() {
        player?.pause()
        isPlaying = false
    }
    
    func seek(to time: Double) {
        let targetTime = CMTime(seconds: time, preferredTimescale: 600)
        player?.seek(to: targetTime, toleranceBefore: .zero, toleranceAfter: .zero)
    }
    
    deinit {
        cleanup()
    }
}

// MARK: - AI优化模块
struct AIOptimizationModule: View {
    let selectedVideo: URL?
    let mediaType: MediaType
    let onSelectMedia: () -> Void
    @Binding var isTranscribing: Bool
    let editableSegments: [EditableSegment]
    let transcriptionLanguage: String
    let onReTranscribe: (EditableSegment) -> Void
    @ObservedObject var playerManager: PlayerManager // 接受外部的播放器管理器
    @State private var subtitleMode: SubtitleMode = .none
    @State private var optimizedSegments: [EditableSegment] = []

    // 转录设置相关状态
    @State private var showTranscriptionSettings = false
    @State private var showSettings = false // 添加完整设置页面状态
    @AppStorage("selectedModel") private var selectedModel = "small"
    @AppStorage("enableVAD") private var enableVAD = true
    @AppStorage("transcriptionLanguage") private var localTranscriptionLanguage: String = "en"

    // 视频片段导出状态
    @State private var clipStartTime: String = "00:00:00"
    @State private var clipEndTime: String = "00:00:00"

    var body: some View {
        GeometryReader { geometry in
            HStack(spacing: 0) {
                // 左侧：独立的媒体播放器
                VStack(spacing: 0) {
                    if let url = selectedVideo {
                        if mediaType == .video {
                            VStack(spacing: 0) {
                                VideoPlayer(
                                    url: url,
                                    player: playerManager.player ?? AVPlayer(url: url),
                                    segments: optimizedSegments,
                                    currentTime: playerManager.currentTime,
                                    subtitleMode: $subtitleMode
                                )
                                .frame(height: geometry.size.height * 0.6)
                                .frame(maxWidth: .infinity)
                                .clipped()

                                // 视频片段导出区域 - 紧贴视频播放器
                                // 字幕控制按钮 + AI功能按钮
                                HStack {
                                    SubtitleControlButton(subtitleMode: $subtitleMode)
                                        .disabled(!optimizedSegments.contains(where: { segment in
                                            !segment.text.isEmpty && (segment.translatedText?.isEmpty == false)
                                        }))

                                    Spacer()

                                    // AI功能按钮组
                                    Button(action: {
                                        showTranscriptionSettings = true
                                        Task {
                                            await transcribeAudio()
                                        }
                                    }) {
                                        Label("转录音频", systemImage: "waveform")
                                            .labelStyle(TitleAndIconLabelStyle())
                                    }
                                    .buttonStyle(.borderedProminent)
                                    .disabled(selectedVideo == nil || isTranscribing)

                                    Button(action: {
                                        showSettings = true
                                    }) {
                                        Image(systemName: "gear")
                                    }
                                    .buttonStyle(.bordered)
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)


                                VStack(spacing: 8) {
                                    HStack(spacing: 8) {
                                        Text("开始")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                        TextField("00:00:00", text: $clipStartTime)
                                            .textFieldStyle(RoundedBorderTextFieldStyle())
                                            .frame(width: 80)
                                            .onChange(of: clipStartTime) { _ in
                                                updatePlayerTime(from: clipStartTime)
                                            }

                                        Text("结束")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                        TextField("00:00:00", text: $clipEndTime)
                                            .textFieldStyle(RoundedBorderTextFieldStyle())
                                            .frame(width: 80)
                                            .onChange(of: clipEndTime) { _ in
                                                updatePlayerTime(from: clipEndTime)
                                            }

                                        Button(action: {
                                            exportVideoClip()
                                        }) {
                                            Label("导出片段", systemImage: "square.and.arrow.up")
                                                .labelStyle(TitleAndIconLabelStyle())
                                        }
                                        .buttonStyle(.bordered)
                                        .disabled(selectedVideo == nil)
                                    }
                                }
                                .padding(.horizontal, 12)
                                .padding(.vertical, 8)
                                .background(Color(NSColor.controlBackgroundColor))
                            }
                        } else if mediaType == .audio {
                            VStack(spacing: 0) {
                                AudioPlayer(
                                    url: url,
                                    player: playerManager.player ?? AVPlayer(url: url),
                                    segments: optimizedSegments,
                                    currentTime: playerManager.currentTime
                                )
                                .frame(height: geometry.size.height * 0.6)
                                .frame(maxWidth: .infinity)
                                .clipped()

                                Rectangle()
                                    .frame(height: 1)
                                    .foregroundColor(Color.gray.opacity(0.3))
                            }
                        }
                    } else {
                        VStack(spacing: 0) {
                            VStack {
                                Image(systemName: "video.slash")
                                    .font(.system(size: 48))
                                    .foregroundColor(.secondary)

                                Text("请选择视频或音频文件")
                                    .font(.title2)
                                    .foregroundColor(.secondary)

                                Button("选择本地媒体") {
                                    onSelectMedia()
                                }
                                .buttonStyle(BorderedProminentButtonStyle())
                            }
                            .frame(height: geometry.size.height * 0.6)
                            .frame(maxWidth: .infinity)
                            .background(Color(NSColor.controlBackgroundColor))
                            .clipped()

                            Rectangle()
                                .frame(height: 1)
                                .foregroundColor(Color.gray.opacity(0.3))
                        }
                    }
                }
                .frame(width: geometry.size.width * 0.4)

                // 右侧：AI翻译优化界面
                VStack(spacing: 0) {
                    if selectedVideo != nil {
                        // 选择媒体后立即显示转录设置
                        if editableSegments.isEmpty && !isTranscribing {
                            VStack(spacing: 16) {
                                Image(systemName: "waveform.and.mic")
                                    .font(.system(size: 48))
                                    .foregroundColor(.blue)

                                Text("转录音频设置")
                                    .font(.title2)
                                    .fontWeight(.semibold)

                                Text("请配置转录参数并开始转录")
                                    .font(.body)
                                    .foregroundColor(.secondary)

                                // 转录设置按钮
                                Button("配置转录设置") {
                                    showTranscriptionSettings = true
                                }
                                .buttonStyle(BorderedProminentButtonStyle())
                                .controlSize(.large)
                            }
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                        } else {
                            // AI翻译优化界面
                            AITranslationView(
                                originalSegments: editableSegments,
                                optimizedSegments: .constant(optimizedSegments),
                                transcriptionLanguage: transcriptionLanguage,
                                player: Binding(
                                    get: { playerManager.player },
                                    set: { playerManager.player = $0 }
                                ),
                                subtitleMode: $subtitleMode,
                                isTranscribing: $isTranscribing,
                                onReTranscribe: onReTranscribe,
                                onExportVideoClip: { [self] startTime, endTime in
                                    guard let url = selectedVideo else { return }
                                    let startSeconds = self.timeStringToSeconds(startTime) ?? 0
                                    let endSeconds = self.timeStringToSeconds(endTime) ?? 0
                                    self.exportVideoClipWithTimes(url: url, startTime: startSeconds, endTime: endSeconds)
                                }
                            )
                        }
                    } else {
                        VStack {
                            Image(systemName: "wand.and.stars")
                                .font(.system(size: 48))
                                .foregroundColor(.secondary)

                            Text("AI断句/翻译优化")
                                .font(.title2)
                                .foregroundColor(.secondary)

                            Text("请先选择媒体文件")
                                .font(.body)
                                .foregroundColor(.secondary)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                    }
                }
                .frame(maxWidth: .infinity)
            }
        }
        .sheet(isPresented: $showTranscriptionSettings) {
            TranscriptionSettingsView(
                transcriptionLanguage: $localTranscriptionLanguage,
                selectedModel: $selectedModel,
                enableVAD: $enableVAD,
                onConfirm: {
                    showTranscriptionSettings = false
                    // 开始转录
                    startTranscription()
                },
                onCancel: {
                    showTranscriptionSettings = false
                }
            )
            .frame(width: 500, height: 400)
        }
        .sheet(isPresented: $showSettings) {
            SettingsView()
                .frame(width: 800, height: 600)
        }
    }

    // 开始转录函数
    private func startTranscription() {
        guard let videoURL = selectedVideo else { return }

        Task {
            isTranscribing = true
            defer { isTranscribing = false }

            do {
                // 这里需要调用实际的转录逻辑
                // 由于我们在模块内部，需要通过回调或其他方式来处理
                print("开始转录: \(videoURL)")
                // TODO: 实现转录逻辑
            } catch {
                print("转录失败: \(error)")
            }
        }
    }

    // MARK: - 辅助函数

    /// 将时间字符串转换为秒数
    private func timeStringToSeconds(_ timeString: String) -> Double? {
        let components = timeString.split(separator: ":").map { String($0) }
        guard components.count == 3,
              let hours = Double(components[0]),
              let minutes = Double(components[1]),
              let seconds = Double(components[2]) else {
            return nil
        }
        return hours * 3600 + minutes * 60 + seconds
    }

    /// 更新播放器时间
    private func updatePlayerTime(from timeString: String) {
        guard let player = playerManager.player,
              let seconds = timeStringToSeconds(timeString) else { return }

        let time = CMTime(seconds: seconds, preferredTimescale: 600)
        player.seek(to: time)
    }

    /// 导出视频片段
    private func exportVideoClip() {
        guard let url = selectedVideo else { return }

        let startSeconds = timeStringToSeconds(clipStartTime) ?? 0
        let endSeconds = timeStringToSeconds(clipEndTime) ?? 0

        exportVideoClipWithTimes(url: url, startTime: startSeconds, endTime: endSeconds)
    }

    /// 导出视频片段
    private func exportVideoClipWithTimes(url: URL, startTime: Double, endTime: Double) {
        // 这里需要实现导出逻辑，或者通过回调传递给父视图
        print("导出视频片段: \(url), 开始: \(startTime), 结束: \(endTime)")
        // TODO: 实现实际的导出逻辑
    }
}

// MARK: - 文本检测模块
struct TextDetectionModule: View {
    let selectedVideo: URL?
    let mediaType: MediaType
    let onSelectMedia: () -> Void
    @Binding var recognitionLevel: VNRequestTextRecognitionLevel
    @Binding var recognitionLanguage: String
    @Binding var isDetecting: Bool
    @Binding var detectionProgress: Double
    let videoProcessor: VideoProcessor
    @Binding var detectedFrames: [DetectedFrame]
    @Binding var searchKeyword: String
    @Binding var keywordCount: Int
    @ObservedObject var playerManager: PlayerManager // 接受外部的播放器管理器

    var body: some View {
        GeometryReader { geometry in
            HStack(spacing: 0) {
                // 左侧：独立的视频播放器（仅用于文本检测）
                VStack(spacing: 0) {
                    if let url = selectedVideo, mediaType == .video {
                        VStack(spacing: 0) {
                            VideoPlayer(
                                url: url,
                                player: playerManager.player ?? AVPlayer(url: url),
                                segments: [],
                                currentTime: playerManager.currentTime,
                                subtitleMode: .constant(.none)
                            )
                            .frame(height: geometry.size.height * 0.6)
                            .frame(maxWidth: .infinity)
                            .clipped()

                            // 文本检测控制按钮区域 - 紧贴视频播放器
                            VStack(spacing: 12) {
                                // 选择视频按钮
                                Button(action: {
                                    onSelectMedia()
                                }) {
                                    HStack {
                                        Image(systemName: "plus.circle")
                                        Text("选择视频")
                                    }
                                }
                                .buttonStyle(.borderedProminent)
                                .frame(maxWidth: .infinity)
                                .padding(.horizontal, 8)

                                // 识别级别选择 - 占满宽度
                                VStack(alignment: .leading, spacing: 4) {
                                    Picker("识别级别", selection: $recognitionLevel) {
                                        Text("快速").tag(VNRequestTextRecognitionLevel.fast)
                                        Text("精确").tag(VNRequestTextRecognitionLevel.accurate)
                                    }
                                    .pickerStyle(SegmentedPickerStyle())
                                    .frame(maxWidth: .infinity)
                                }
                                .padding(.horizontal, 8)

                                // 语言选择 - 占满宽度
                                VStack(alignment: .leading, spacing: 4) {
                                    Picker("识别语言", selection: $recognitionLanguage) {
                                        Text("中文").tag("zh-Hans")
                                        Text("英文").tag("en")
                                        Text("自动").tag("auto")
                                    }
                                    .pickerStyle(SegmentedPickerStyle())
                                    .frame(maxWidth: .infinity)
                                }
                                .padding(.horizontal, 8)

                                // 检测按钮
                                Button(action: {
                                    if selectedVideo == nil || mediaType != .video {
                                        let alert = NSAlert()
                                        alert.messageText = "请先选择视频文件"
                                        alert.informativeText = "检测视频文本功能仅支持视频文件。"
                                        alert.addButton(withTitle: "确定")
                                        alert.runModal()
                                        return
                                    }
                                    Task {
                                        isDetecting = true
                                        detectionProgress = 0
                                        detectedFrames = try await videoProcessor.processVideo(
                                            url: selectedVideo!,
                                            recognitionLevel: recognitionLevel,
                                            progressHandler: { progress in
                                                DispatchQueue.main.async {
                                                    detectionProgress = progress
                                                }
                                            }
                                        )
                                        isDetecting = false
                                    }
                                }) {
                                    Label("检测视频文本", systemImage: "doc.text.viewfinder")
                                        .labelStyle(TitleAndIconLabelStyle())
                                }
                                .buttonStyle(.borderedProminent)
                                .disabled(selectedVideo == nil || isDetecting)
                                .padding(.horizontal, 8)
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color(NSColor.controlBackgroundColor))

                            // 分隔线
                            Rectangle()
                                .frame(height: 1)
                                .foregroundColor(Color.gray.opacity(0.3))
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                        }
                    } else {
                        VStack(spacing: 0) {
                            VStack {
                                Image(systemName: "text.viewfinder")
                                    .font(.system(size: 48))
                                    .foregroundColor(.secondary)

                                Text("文本检测")
                                    .font(.title2)
                                    .foregroundColor(.secondary)

                                Text("请选择视频文件进行文本检测")
                                    .font(.body)
                                    .foregroundColor(.secondary)

                                Button("选择视频文件") {
                                    onSelectMedia()
                                }
                                .buttonStyle(BorderedProminentButtonStyle())
                            }
                            .frame(height: geometry.size.height * 0.6)
                            .frame(maxWidth: .infinity)
                            .background(Color(NSColor.controlBackgroundColor))
                            .clipped()

                            Rectangle()
                                .frame(height: 1)
                                .foregroundColor(Color.gray.opacity(0.3))
                        }
                    }
                }
                .frame(width: geometry.size.width * 0.4)

                // 右侧：文本检测结果
                VStack(spacing: 0) {
                    // 进度显示
                    if isDetecting {
                        VStack(alignment: .leading) {
                            ProgressView(value: detectionProgress, total: 100)
                                .progressViewStyle(.linear)
                                .frame(maxWidth: .infinity)
                            Text("正在检测视频文本... \(Int(detectionProgress))%")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding()
                        .background(Color(NSColor.controlBackgroundColor))
                        .cornerRadius(8)
                    }

                    // 检测结果列表
                    ScrollView {
                        LazyVGrid(columns: [
                            GridItem(.flexible()),
                            GridItem(.flexible()),
                            GridItem(.flexible())
                        ], spacing: 10) {
                            ForEach(videoProcessor.textSegments.indices, id: \.self) { index in
                                let segment = videoProcessor.textSegments[index]
                                let highlightedText = highlightText(
                                    segment.text,
                                    keyword: searchKeyword
                                )

                                TextSegmentCard(segment: segment, highlightedText: highlightedText) {
                                    playerManager.seek(to: segment.startTime)
                                }
                                .transition(.opacity)
                            }
                        }
                        .padding()
                    }

                    // 搜索框
                    Divider()
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.gray)
                        TextField("输入关键字搜索视频文本...", text: $searchKeyword)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                        Text("找到 \(keywordCount) 个关键词")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .onChange(of: searchKeyword) { newValue in
                        keywordCount = videoProcessor.countKeywords(keyword: newValue)
                    }
                }
                .frame(maxWidth: .infinity)
            }
        }
    }
}

// MARK: - 硬字幕提取模块
struct HardSubtitleExtractionModule: View {
    let selectedVideo: URL?
    let mediaType: MediaType
    let onSelectMedia: () -> Void
    @Binding var isSelectingSubtitleRegion: Bool
    @Binding var subtitleRegion: CGRect
    @Binding var isExtractingSubtitles: Bool
    @Binding var subtitleExtractionProgress: Double
    let videoProcessor: VideoProcessor
    let onExtractSubtitles: () async -> Void
    let onExtractHardSubtitlesEfficiently: () async -> Void // 新增高效提取回调
    @ObservedObject var playerManager: PlayerManager // 接受外部的播放器管理器

    var body: some View {
        GeometryReader { geometry in
            HStack(spacing: 0) {
                // 左侧：视频播放器 + 控制按钮
                VStack(spacing: 0) {
                    if let url = selectedVideo, mediaType == .video {
                        VideoPlayer(
                            url: url,
                            player: playerManager.player ?? AVPlayer(url: url),
                            segments: [],
                            currentTime: playerManager.currentTime,
                            subtitleMode: .constant(.none)
                        )
                        .frame(height: geometry.size.height * 0.6)
                        .frame(maxWidth: .infinity)
                        .clipped()

                        // 硬字幕提取按钮组 - 紧贴视频播放器
                        VStack(spacing: 8) {
                            // 第一行：选择视频按钮
                            Button(action: {
                                onSelectMedia()
                            }) {
                                HStack {
                                    Image(systemName: "plus.circle")
                                    Text("选择视频")
                                }
                            }
                            .buttonStyle(.borderedProminent)
                            .frame(maxWidth: .infinity)

                            // 第二行：硬字幕功能按钮
                            HStack(spacing: 8) {
                                Button(action: {
                                    videoProcessor.subtitleRegion.isSelected = false
                                    isSelectingSubtitleRegion = true
                                }) {
                                    HStack {
                                        Image(systemName: "viewfinder")
                                        Text("选择字幕区域")
                                    }
                                }
                                .buttonStyle(.bordered)
                                .disabled(selectedVideo == nil)

                                Button(action: {
                                    // 确认区域功能
                                    videoProcessor.subtitleRegion.isSelected = true
                                    isSelectingSubtitleRegion = false
                                }) {
                                    HStack {
                                        Image(systemName: "checkmark.circle")
                                        Text("确认区域")
                                    }
                                }
                                .buttonStyle(.bordered)
                                .disabled(selectedVideo == nil || !isSelectingSubtitleRegion)

                                Button(action: {
                                    Task {
                                        await onExtractHardSubtitlesEfficiently()
                                    }
                                }) {
                                    HStack {
                                        Image(systemName: "wand.and.stars")
                                        Text("提取字幕")
                                    }
                                }
                                .buttonStyle(.borderedProminent)
                                .disabled(selectedVideo == nil || isExtractingSubtitles || !videoProcessor.subtitleRegion.isSelected)
                            }
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color(NSColor.controlBackgroundColor))

                    } else {
                        VStack {
                            Image(systemName: "text.bubble")
                                .font(.system(size: 48))
                                .foregroundColor(.secondary)

                            Text("硬字幕提取")
                                .font(.title2)
                                .foregroundColor(.secondary)

                            Text("请选择视频文件进行硬字幕提取")
                                .font(.body)
                                .foregroundColor(.secondary)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color(NSColor.controlBackgroundColor))
                    }
                }
                .frame(width: geometry.size.width * 0.4)

                // 右侧：硬字幕提取结果
                VStack(spacing: 0) {
                    // 进度显示
                    if isExtractingSubtitles {
                        VStack(alignment: .leading, spacing: 8) {
                            ProgressView(value: subtitleExtractionProgress, total: 100)
                                .progressViewStyle(.linear)
                                .frame(maxWidth: .infinity)
                            Text("正在提取硬字幕... \(Int(subtitleExtractionProgress))%")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                    }

                    // 提取结果列表
                    ScrollView {
                        LazyVStack(alignment: .leading, spacing: 8) {
                            ForEach(videoProcessor.textSegments.indices, id: \.self) { index in
                                let segment = videoProcessor.textSegments[index]

                                VStack(alignment: .leading, spacing: 4) {
                                    HStack {
                                        Text(formatTime(segment.startTime))
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                        Text("→")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                        Text(formatTime(segment.endTime))
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                        Spacer()
                                        Button(action: {
                                            playerManager.seek(to: segment.startTime)
                                        }) {
                                            Image(systemName: "play.circle")
                                        }
                                        .buttonStyle(.plain)
                                    }

                                    Text(segment.text)
                                        .font(.body)
                                        .multilineTextAlignment(.leading)
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(Color(NSColor.controlBackgroundColor))
                                .cornerRadius(8)
                            }
                        }
                        .padding(.horizontal, 16)
                    }

                    Spacer()
                }
                .frame(maxWidth: .infinity)
            }
        }
    }

    // MARK: - 辅助函数

    private func formatTime(_ time: Double) -> String {
        let hours = Int(time) / 3600
        let minutes = Int(time) % 3600 / 60
        let seconds = Int(time) % 60
        return String(format: "%02d:%02d:%02d", hours, minutes, seconds)
    }


}

@available(macOS 15.0, *)
struct ContentView: View {
    @State private var whisperKit: WhisperKit?
    @State private var selectedVideo: URL?
    @State private var recognitionLevel: VNRequestTextRecognitionLevel = .accurate
    @State private var recognitionLanguage: String = "en-US" // 默认选择英文
    @AppStorage("transcriptionLanguage") private var transcriptionLanguage: String = "en"
    @State private var isTranscribing = false // 控制转录进度指示器的显示
    // @State private var transcriptionText: String = ""
    @State private var searchKeyword = "" // 搜索关键词
    @State private var translationSearchKeyword = "" // 翻译结果搜索关键词
    @State private var detectedFrames: [DetectedFrame] = [] // 存储所有检测到的帧
    @State private var transcriptionResults: [String] = [] // 存储转录结果
    @State private var player: AVPlayer?
    @State private var selectedSegment = 0 // 控制显示的分段：0=AI优化，1=文本检测，2=硬字幕提取
    @AppStorage("selectedModel") private var selectedModel = "small" // 选择模型
    @State private var progress: Double = 0.0 // 进度条的值
    @State private var modelPath: String?
    @State private var srtContent: String = "" // 存储 SRT 格式的内容
    @State private var editableSegments: [EditableSegment] = []// 确保定义
    @State private var selectedSegmentId: UUID?
    @State private var cursorPosition: Int?
    @State private var editHistory = EditHistory()
    @State private var draggedSegmentId: UUID?
    @State private var clipStartTime: String = "00:00:00"
    @State private var clipEndTime: String = "00:00:00"
    @FocusState private var startTimeFocused: Bool
    @FocusState private var endTimeFocused: Bool
    @State private var showTranslation = false
    @State private var configuration: TranslationSession.Configuration?
    @State private var selectedFrames: Set<UUID> = []
    @State private var mediaType: MediaType = .video // 用于区分媒体类型
    @State private var isPlaying: Bool = false
    @State private var currentTime: Double = 0
    @State private var duration: Double = 0
    @State private var timer: Timer?
    @State private var selectedTranslationSegments: Set<UUID> = [] // 存储选中的段落 ID
    @State private var optimizedSegments: [EditableSegment] = [] // 添加这一行
    @State private var cacheUpdateTimer: Timer? // 添加定时器
    @Environment(\.scenePhase) private var scenePhase // 添加场景阶段监测
    @State private var showSettings = false

    // 🔑 转录设置对话框
    @State private var showTranscriptionSettings = false
    @AppStorage("enableVAD") private var enableVAD = true

    // 添加字幕显示模式
    @State private var subtitleMode: SubtitleMode = .none
    @State private var isProcessing: Bool = false //控制视频处理进度指示器的显示
    @State private var subtitleOverlay: NSTextField? // 添加字幕叠加层
    // 在 ContentView 中添加进度状态
    @State private var detectionProgress: Double = 0
    @State private var isDetecting: Bool = false
    @State private var keywordCount: Int = 0 // 关键词数量

    // 添加状态变量
    @State private var isSelectingSubtitleRegion: Bool = false
    @State private var subtitleRegion: CGRect = .zero
    @State private var isExtractingSubtitles: Bool = false
    @State private var subtitleExtractionProgress: Double = 0

    @StateObject private var videoProcessor = VideoProcessor()
    // 为每个模块提供独立的播放器管理器
    @StateObject private var aiPlayerManager = PlayerManager()
    @StateObject private var textDetectionPlayerManager = PlayerManager()
    @StateObject private var hardSubtitlePlayerManager = PlayerManager()

    @State private var lastScrolledIndex: Int = -1  // 添加这一行
    @State private var scrollProxy: ScrollViewProxy? = nil // 用于获取 ScrollView 的代理

    // 在 ContentView 中添加静态实例以便在 AppDelegate 中访问
    static var shared: ContentView?

    // 添加一个方法来切换标签页
    func switchToTab(_ tab: Int) {
        selectedSegment = tab
    }

    // 添加一个对象来处理缓存
    private class CacheManager {
        static let shared = CacheManager()
        
        func saveAllCache() {
            if let contentView = ContentView.shared {
                contentView.saveAllCache()
            }
        }
    }

    init() {
        ContentView.shared = self
        
        // 使用 CacheManager 来处理缓存保存，每5分钟自动缓存
        Timer.scheduledTimer(withTimeInterval: 300, repeats: true) { _ in
            CacheManager.shared.saveAllCache()
        }
    }

    var body: some View {
        GeometryReader { geometry in
            HStack(spacing: 0) {
                // 最左侧：可折叠侧边栏
                CollapsibleSidebar(selectedSegment: $selectedSegment)

                // 主要内容区域：根据选择的模块显示不同界面
                Group {
                    switch selectedSegment {
                    case 0: // AI优化模块
                        AIOptimizationModule(
                            selectedVideo: selectedVideo,
                            mediaType: mediaType,
                            onSelectMedia: selectMedia,
                            isTranscribing: $isTranscribing,
                            editableSegments: editableSegments,
                            transcriptionLanguage: transcriptionLanguage,
                            onReTranscribe: { segment in
                                reTranscribeSegment(segment)
                            },
                            playerManager: aiPlayerManager
                        )

                    case 1: // 文本检测模块
                        TextDetectionModule(
                            selectedVideo: selectedVideo,
                            mediaType: mediaType,
                            onSelectMedia: selectMedia,
                            recognitionLevel: $recognitionLevel,
                            recognitionLanguage: $recognitionLanguage,
                            isDetecting: $isDetecting,
                            detectionProgress: $detectionProgress,
                            videoProcessor: videoProcessor,
                            detectedFrames: $detectedFrames,
                            searchKeyword: $searchKeyword,
                            keywordCount: $keywordCount,
                            playerManager: textDetectionPlayerManager
                        )

                    case 2: // 硬字幕提取模块
                        HardSubtitleExtractionModule(
                            selectedVideo: selectedVideo,
                            mediaType: mediaType,
                            onSelectMedia: selectMedia,
                            isSelectingSubtitleRegion: $isSelectingSubtitleRegion,
                            subtitleRegion: $subtitleRegion,
                            isExtractingSubtitles: $isExtractingSubtitles,
                            subtitleExtractionProgress: $subtitleExtractionProgress,
                            videoProcessor: videoProcessor,
                            onExtractSubtitles: extractSubtitles,
                            onExtractHardSubtitlesEfficiently: extractHardSubtitlesEfficiently,
                            playerManager: hardSubtitlePlayerManager
                        )

                    default:
                        AIOptimizationModule(
                            selectedVideo: selectedVideo,
                            mediaType: mediaType,
                            onSelectMedia: selectMedia,
                            isTranscribing: $isTranscribing,
                            editableSegments: editableSegments,
                            transcriptionLanguage: transcriptionLanguage,
                            onReTranscribe: { segment in
                                reTranscribeSegment(segment)
                            },
                            playerManager: aiPlayerManager
                        )
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)


            }
        }
        .background(Color(NSColor.windowBackgroundColor)) // 统一的窗口背景色
        .onChange(of: scenePhase) { newPhase in
            switch newPhase {
            case .background:
                // 应用进入后台时保存所有缓存
                saveAllCache()
            case .inactive:
                // 应用变为非活动状态时保存所有缓存
                saveAllCache()
            default:
                break
            }
        }
        .onAppear {
            setupPlayer()
            // 在应用启动时加载缓存
            if let mediaURL = selectedVideo {
                loadAllCache()
            }
            //自动同步刷新授权
            Task {
                await LicenseManager.shared.updateLicenseStatusAll()
            }
            // 监听 App 失去焦点、即将退出、窗口关闭等事件，自动保存缓存
            NotificationCenter.default.addObserver(
                forName: NSApplication.willResignActiveNotification,
                object: nil,
                queue: .main
            ) { _ in
                saveAllCache()
            }
            NotificationCenter.default.addObserver(
                forName: NSApplication.willTerminateNotification,
                object: nil,
                queue: .main
            ) { _ in
                saveAllCache()
            }
            NotificationCenter.default.addObserver(
                forName: NSWindow.willCloseNotification,
                object: nil,
                queue: .main
            ) { _ in
                saveAllCache()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ForceSaveAllCache"))) { _ in
            saveAllCache()
        }
        .onDisappear {
            // 移除监听
            NotificationCenter.default.removeObserver(self, name: NSApplication.willResignActiveNotification, object: nil)
            NotificationCenter.default.removeObserver(self, name: NSApplication.willTerminateNotification, object: nil)
            NotificationCenter.default.removeObserver(self, name: NSWindow.willCloseNotification, object: nil)
            // 
            timer?.invalidate()
            timer = nil
        }
        .frame(minWidth: 1200, minHeight: 620)
    }

    // 简化数据模型，只保留需要字段
    struct WhisperTranscription: Codable {
        let text: String
        let segments: [Segment]
        let language: String
        
        struct Segment: Codable {
            let start: Double
            let end: Double
            let text: String
            let words: [Word]
            
            // 添加标准初始化方法
            init(start: Double, end: Double, text: String, words: [Word]) {
                self.start = start
                self.end = end
                self.text = text
                self.words = words
            }
            
            // 提供默认空数组，处理缺失words字段的情况
            init(from decoder: Decoder) throws {
                let container = try decoder.container(keyedBy: CodingKeys.self)
                start = try container.decode(Double.self, forKey: .start)
                end = try container.decode(Double.self, forKey: .end)
                text = try container.decode(String.self, forKey: .text)
                
                // 尝试解码words字段，如果不存在则使用空数组
                words = (try? container.decodeIfPresent([Word].self, forKey: .words)) ?? []
            }
            
            enum CodingKeys: String, CodingKey {
                case start, end, text, words
            }
        }
        
        struct Word: Codable {
            let word: String
            let start: Double
            let end: Double
            let probability: Double
        }
    }

    // 翻译结果处理
    private func updateTranslation(response: TranslationSession.Response) {
        guard let index = editableSegments.firstIndex(where: { $0.id.uuidString == response.clientIdentifier }) else {
            return
        }

        // 在保存翻译结果时就处理好格式
        let formattedText = response.targetText
            .components(separatedBy: .whitespaces)
            .filter { !$0.isEmpty }
            .joined(separator: " ")
            .trimmingCharacters(in: .whitespaces)
            editableSegments[index].translatedText = response.targetText // 更新翻译结果
        }

    // 导出字幕
    private func exportSubtitles(type: SubtitleExportType) {
        var srtContent = ""
        for (index, segment) in editableSegments.enumerated() {
            // 处理原始文本，确保单词之间只有一个空格，并去除首尾空格
            let originalText = segment.text
                .components(separatedBy: .whitespaces)
                .filter { !$0.isEmpty }
                .joined(separator: " ") 
                .trimmingCharacters(in: .whitespaces) 
            
            srtContent += "\(index + 1)\n"
            srtContent += "\(formatTimeForSRT(seconds: segment.startTime)) --> \(formatTimeForSRT(seconds: segment.endTime))\n"
            
            switch type {
            case .bilingual:
                // 如果有翻译文本，显示双语
                if let translatedText = segment.translatedText {
                    srtContent += "\(translatedText)\n" // 翻译文本
                    srtContent += "\(originalText)\n" // 原文
                } else {
                    srtContent += "\(originalText)\n" // 只有原文
                }
            case .original:
                // 只导出原文
                srtContent += "\(originalText)\n"
            case .translation:
                // 只导出翻译（如果有）
                if let translatedText = segment.translatedText {
                    srtContent += "\(translatedText)\n"
                } else {
                    srtContent += "\(originalText)\n" // 如果没有翻译，使用原文
                }
            }
            
            srtContent += "\n"
        }
        
        // 保存到文件
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [.text]
        
        // 根据导出类型设置默认文件名
        let filename = switch type {
            case .bilingual: "bilingual_subtitles.srt"
            case .original: "original_subtitles.srt"
            case .translation: "translation_subtitles.srt"
        }
        savePanel.nameFieldStringValue = filename
        
        savePanel.begin { result in
            if result == .OK, let url = savePanel.url {
                do {
                    try srtContent.write(to: url, atomically: true, encoding: .utf8)
                    // print("字幕已保存到: \(url.path)")
                } catch {
                    print("保存字幕文件失败: \(error)")
                }
            }
        }
    }

    // 清理文本
    func cleanText(_ text: String) -> String {
        // 使用正则表达式去除尖括号及其内部的内容
        let cleanedText = text.replacingOccurrences(of: "<[^>]+>", with: "", options: .regularExpression)
        return cleanedText.trimmingCharacters(in: .whitespacesAndNewlines) // 去除首尾空格
    } 
    
    // 选择视频
    func selectVideo() {
        let panel = NSOpenPanel()
        panel.allowedContentTypes = [UTType.movie]
        panel.allowsMultipleSelection = false
        
        if panel.runModal() == .OK {
            guard let selectedVideoURL = panel.url else { return }
            selectedVideo = selectedVideoURL
            player = AVPlayer(url: selectedVideo!)

            // 使用 async/await 处理视频处理
            Task(priority: .userInitiated) { // 设置优先级为 userInitiated
                do {
                    // 处理视频并获取检测到的帧
                    detectedFrames = try await videoProcessor.processVideo(url: selectedVideo!, recognitionLevel: recognitionLevel)
                } catch {
                    print("处理视失败: \(error)")
                }
            }
        }
    }

    // 格式化时间
    func formatTime(seconds: Double) -> String {
        let totalSeconds = Int(seconds)
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let secs = totalSeconds % 60
        
        if hours > 0 {
            return String(format: "%02d:%02d:%02d", hours, minutes, secs)
        } else {
            return String(format: "%02d:%02d", minutes, secs)
        }
    }
    
    // 首先定义一个用于写入 JSON 文件的函数
    func writeJSONFile(result: TranscriptionResult) -> Result<URL, Error> {
        do {
            // 使用临时目录
            let tempDirectory = FileManager.default.temporaryDirectory
            let timestamp = ISO8601DateFormatter().string(from: Date())
            let jsonURL = tempDirectory.appendingPathComponent("transcription_\(timestamp).json")
            
            // 创建 JSON 编码器
            let jsonEncoder = JSONEncoder()
            jsonEncoder.outputFormatting = .prettyPrinted
            
            // 编码并写入文件
            let jsonData = try jsonEncoder.encode(result)
            try jsonData.write(to: jsonURL)
            
            return .success(jsonURL)
        } catch {
            return .failure(error)
        }
    }

    // 自动检测本地模型，没有就联网下载
    func ensureWhisperModel(modelName: String = "base") async throws -> WhisperKit {
        // print("开始加载模型...\(modelName)")
        let modelRepo = "argmaxinc/whisperkit-coreml" // 你实际用的repo
        if let savedModelPath = UserDefaults.standard.string(forKey: "WhisperKitModelPath")?.replacingOccurrences(of: "openai_whisper-[^.]+", with: "openai_whisper-\(modelName)", options: .regularExpression),
           FileManager.default.fileExists(atPath: savedModelPath) {
            print("本地模型路径：\(savedModelPath)")
            // 本地模型存在，直接加载
            // 初始化 WhisperKit
            let whisperKit = try await WhisperKit(
                // model: modelName,  
                modelFolder: savedModelPath,//本地模型存储路径（String?），如果为 nil，则使用默认路径或自动下载到默认目录。
                logLevel: .debug, 
                prewarm: true, 
                load: true)
            // print("WhisperKit 离线模型加载完成！")
            return whisperKit
        } else {
            // 本地模型不存在，联网下载
            // print("本地模型不存在，开始联网下载...")
            let whisperKit = try await WhisperKit(
                model: modelName, 
                verbose: true, //是否输出详细日志
                logLevel: .info, //日志级别，常见有 .debug（开发调试）、.info（正式环境）、.warning、.error。
                prewarm: true, //是否预热模型（提前加载到内存，推理更快）。
                load: true) //是否立即加载模型
            guard let downloadedModelPath = whisperKit.modelFolder?.path else {
                throw NSError(domain: "com.example.VideoTextDetector", code: -2, userInfo: [NSLocalizedDescriptionKey: "模型下载后路径获取失败"])
            }
            UserDefaults.standard.set(downloadedModelPath, forKey: "WhisperKitModelPath")
            // print("模型下载完成，已保存路径: \(downloadedModelPath)")
            return whisperKit
        }
    }

    // 修改转录函数
    func transcribeAudio() async {
        // 🔑 最终的专业版权限检查（双重保障）
        if ProFeatureHelper.shared.isProModel(selectedModel) && !LicenseManager.shared.isPro() {
            // 这种情况理论上不应该发生，因为设置对话框已经处理了
            // 但作为最后的安全检查
            DispatchQueue.main.async {
                let alert = NSAlert()
                alert.messageText = "无法使用专业模型"
                alert.informativeText = "检测到您尝试使用专业模型 \"\(self.selectedModel)\"，但当前没有专业版权限。将自动切换到免费模型。"
                alert.addButton(withTitle: "确定")
                alert.runModal()

                self.selectedModel = "small" // 回退到免费模型
            }
            return
        }
        
        // 确保在主线程上设置状态
        await MainActor.run {
            isTranscribing = true // 开始转录时显示进度指示器
        }
        
        var temporaryAudioPath: String? = nil
        
        Task {
            do {
                // 根据媒体类型获取音频路径
                let audioPath: String
                if mediaType == .video {
                    // 如果是视频，需要先提取音频
                    audioPath = try extractAudio(from: selectedVideo!)
                    temporaryAudioPath = audioPath
                    // print("临时音频文件已创建：\(audioPath)")   
                } else if mediaType == .audio {
                    // 如果是音频文件，直接使用其路径
                    audioPath = selectedVideo!.path
                } else {
                    throw NSError(domain: "com.example.VideoTextDetector", 
                                code: -1, 
                                userInfo: [NSLocalizedDescriptionKey: "未选择媒体文件"])
                }

                // defer 块会在函数结束时执行，无论是正常结束还是抛出错误
                defer {
                    // 清理临时音频文件
                    if let tempPath = temporaryAudioPath {
                        do {
                            try FileManager.default.removeItem(atPath: tempPath)
                            // print("已删除临时音频文件：\(tempPath)")
                        } catch {
                            print("删除临时音频文件失败：\(error.localizedDescription)")
                        }
                    }
                    // 在主线程上更新状态
                    Task { @MainActor in
                        isTranscribing = false
                    }
                }

                // 自动检测/下载模型
                do {
                    let whisperKit = try await ensureWhisperModel(modelName: selectedModel)
                    // 模型加载成功弹窗
                    // DispatchQueue.main.async {
                    //     let alert = NSAlert()
                    //     alert.messageText = "模型加载成功"
                    //     alert.informativeText = "WhisperKit模型 \(selectedModel) 已成功加载。"
                    //     alert.addButton(withTitle: "确定")
                    //     alert.runModal()
                    // }

                    // 🔑 根据用户设置配置转录选项
                    let options = DecodingOptions(
                        verbose: true,
                        task: .transcribe,
                        language: transcriptionLanguage,
                        skipSpecialTokens: true,
                        withoutTimestamps: false,
                        wordTimestamps: true,
                        chunkingStrategy: enableVAD ? .vad : nil 
                    )
                    
                    // 转录音频
                    let transcriptionResults = try await whisperKit.transcribe(
                        audioPath: audioPath,
                        decodeOptions: options
                    )

                    let mergedResult = TranscriptionUtilities.mergeTranscriptionResults(transcriptionResults)
                    // print("合并后的结果: \(mergedResult)")
                    
                    // 保存 JSON 文件并处理转录结果
                    // for result in transcriptionResults {
                    //     let saveResult = writeJSONFile(result: result)
                    //     switch saveResult {
                    //     case .success(let jsonURL):
                    //         // print("JSON 文件已保存到临时目录: \(jsonURL.path)")
                    //         // 使用临时文件创建可编辑段落
                    //         createEditableSegmentsFromJSON(jsonURL)
                    //         self.optimizedSegments = self.editableSegments //每次转录音频后，都会用新结果覆盖 optimizedSegments
                            
                    //         // 可选：在处理完后删除临时文件
                    //         try? FileManager.default.removeItem(at: jsonURL)
                            
                    //     case .failure(let error):
                    //          print("保存 JSON 文件失败: \(error.localizedDescription)")
                    //     }
                    // }

                    let saveResult = writeJSONFile(result: mergedResult)
                    switch saveResult {
                    case .success(let jsonURL):
                        print("JSON 文件已保存到临时目录: \(jsonURL.path)")
                        // 使用临时文件创建可编辑段落
                        createEditableSegmentsFromJSON(jsonURL)
                        self.optimizedSegments = self.editableSegments //每次转录音频后，都会用新结果覆盖 optimizedSegments
                        
                        // 可选：在处理完后删除临时文件
                        try? FileManager.default.removeItem(at: jsonURL)
                        
                    case .failure(let error):
                            print("保存 JSON 文件失败: \(error.localizedDescription)")
                    }
                    
                    // 自动跳转到转录结果界面
                    DispatchQueue.main.async {
                        selectedSegment = 1 // 设置为AI翻译优化标签页
                        isTranscribing = false // 转录完成后隐藏进度指示器
                    }
                } catch {
                    // 模型加载失败弹窗
                    DispatchQueue.main.async {
                        let alert = NSAlert()
                        alert.messageText = "模型加载失败"
                        alert.informativeText = "WhisperKit模型加载失败：\(error.localizedDescription)"
                        alert.addButton(withTitle: "确定")
                        alert.runModal()
                    }
                    throw error
                }
            } catch {
                // print("转录错误: \(error.localizedDescription)")
                // 确保在错误发生时也在主线程上更新状态
                await MainActor.run {
                    isTranscribing = false
                }
            }
        }
    }
    
    func formatTimeForSRT(seconds: Double) -> String {
        let hours = Int(seconds) / 3600
        let minutes = (Int(seconds) % 3600) / 60
        let secs = Int(seconds) % 60
        let milliseconds = Int((seconds - Double(Int(seconds))) * 1000)
        
        return String(format: "%02d:%02d:%02d,%03d", hours, minutes, secs, milliseconds)
    }

    func extractAudio(from videoURL: URL) throws -> String {
        // 提取音频的逻辑
        let asset = AVAsset(url: videoURL)
        let outputURL = FileManager.default.temporaryDirectory.appendingPathComponent(UUID().uuidString).appendingPathExtension("m4a")
        
        guard let exportSession = AVAssetExportSession(asset: asset, presetName: AVAssetExportPresetPassthrough) else {
            throw NSError(domain: "com.example.VideoTextDetector", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法创建导出会话"])
        }
        
        exportSession.outputURL = outputURL
        exportSession.outputFileType = .m4a
        
        // 使用 DispatchGroup 等待导出完成
        let group = DispatchGroup()
        group.enter()

        exportSession.exportAsynchronously {
            switch exportSession.status {
            case .completed:
                 print("音频导出成功: \(outputURL.path)")
                // 更新进度
                DispatchQueue.main.async {
                    progress = 100.0 // 假设导出完成时进度为100%
                }
            case .failed:
                 print("音频导出失败: \(exportSession.error?.localizedDescription ?? "未知错误")")
            default:
                 print("音频导出状态: \(exportSession.status)")
            }
            group.leave()
        }
        
        group.wait() // 等待导出完成
        return outputURL.path // 返回出路径
    }
    
    func searchTranscriptionResults(keyword: String) -> [String] {
        guard !keyword.isEmpty else { return transcriptionResults }
        return transcriptionResults.filter { segment in
            segment.lowercased().contains(keyword.lowercased())
        }
    }

    func selectSegment(_ id: UUID) {
        selectedSegmentId = id
        cursorPosition = nil
    }
    
    func mergeSegments() {
        saveState() // 保存当状态
        guard let currentIndex = editableSegments.firstIndex(where: { $0.id == selectedSegmentId }),
              currentIndex < editableSegments.count - 1 else { return }
        
        var mergedSegment = editableSegments[currentIndex]
        let nextSegment = editableSegments[currentIndex + 1]
        
        mergedSegment.words.append(contentsOf: nextSegment.words)
        
        editableSegments.remove(at: currentIndex + 1)
        editableSegments[currentIndex] = mergedSegment
        
        // 添加缓存更新
        scheduleCacheUpdate()
    }
    
    func splitSegmentAtCursor() {
        saveState() // 存当前状态
        guard let currentIndex = editableSegments.firstIndex(where: { $0.id == selectedSegmentId }),
              let cursor = cursorPosition else { return }
        
        let segment = editableSegments[currentIndex]
        let text = segment.text
        
        // 在光标位分割文本
        let index = text.index(text.startIndex, offsetBy: cursor)
        let firstPart = String(text[..<index])
        let secondPart = String(text[index...])
        
        // 创建新的段落
        let firstSegment = EditableSegment(words: [
            EditableWord(
                word: firstPart.trimmingCharacters(in: .whitespaces),
                start: segment.startTime,
                end: segment.startTime + (segment.endTime - segment.startTime) / 2,
                probability: 1.0
            )
        ])
        
        let secondSegment = EditableSegment(words: [
            EditableWord(
                word: secondPart.trimmingCharacters(in: .whitespaces),
                start: segment.startTime + (segment.endTime - segment.startTime) / 2,
                end: segment.endTime,
                probability: 1.0
            )
        ])
        
        // 更新段落列表
        editableSegments.remove(at: currentIndex)
        editableSegments.insert(contentsOf: [firstSegment, secondSegment], at: currentIndex)
        
        // 更新选中状态
        selectedSegmentId = firstSegment.id
        cursorPosition = nil
        
        // 添加缓存更新
        scheduleCacheUpdate()
    }

    // 从 JSON 创建可编辑段落，解析json
    func createEditableSegmentsFromJSON(_ jsonURL: URL) {
        do {
            let jsonData = try Data(contentsOf: jsonURL)
            let transcription = try JSONDecoder().decode(WhisperTranscription.self, from: jsonData)
            
            // 在主线程更新 UI
            DispatchQueue.main.async {
                let isCJKLanguage = ["ja", "zh", "zh-Hans", "zh-Hant", "ko","th"].contains(transcriptionLanguage)// 使用转录结果中的语言
                
                // 🔑 简单判断：有词级时间戳就用词级，没有就用段落级
                self.editableSegments = transcription.segments.compactMap { segment in
                    if !segment.words.isEmpty {
                        // 有词级时间戳：使用词级处理
                        return EditableSegment(
                            words: segment.words.map { word in
                                EditableWord(
                                    word: cleanText(word.word),
                                    start: word.start,
                                    end: word.end,
                                    probability: word.probability
                                )
                            },
                            isCJKLanguage: isCJKLanguage
                        )
                    } else {
                        // 没有词级时间戳：使用段落级处理
                        return EditableSegment(
                            words: [EditableWord(
                                word: cleanText(segment.text),
                                start: segment.start,
                                end: segment.end,
                                probability: 1.0
                            )],
                            isCJKLanguage: isCJKLanguage
                        )
                    }
                }

                // if let mediaURL = self.selectedVideo {
                //     SubtitleCacheManager.shared.cacheSubtitles(editableSegments, for: mediaURL)
                // }//这里要添加标签页1缓存
                
                // print("已加载 \(self.editableSegments.count) 个字幕段落")
            }
        } catch {
            // print("解析 JSON 文件失败: \(error)")
            // print("详细错误信息: \(error.localizedDescription)")
            
            // 尝试使用更宽松的解析方式
            tryAlternativeJSONParsing(jsonURL)
        }
    }
    
    // 添加一个备用的JSON解析方法，用于处理缺少words字段的情况
    func tryAlternativeJSONParsing(_ jsonURL: URL) {
        do {
            // 定义一个简化的结构，不要求words字段
            struct SimpleWhisperTranscription: Codable {
                let text: String
                let segments: [SimpleSegment]
                let language: String
                
                struct SimpleSegment: Codable {
                    let start: Double
                    let end: Double
                    let text: String
                    // 不包含words字段
                }
            }
            
            let jsonData = try Data(contentsOf: jsonURL)
            let transcription = try JSONDecoder().decode(SimpleWhisperTranscription.self, from: jsonData)
            
            // 在主线程更新UI
            DispatchQueue.main.async {
                let isCJKLanguage = ["ja", "zh", "zh-Hans", "zh-Hant", "ko","th"].contains(transcriptionLanguage)
                
                self.editableSegments = transcription.segments.map { segment in
                    // 所有段落都作为单个单词处理
                    return EditableSegment(
                        words: [EditableWord(
                            word: cleanText(segment.text),
                            start: segment.start,
                            end: segment.end,
                            probability: 1.0
                        )],
                        isCJKLanguage: isCJKLanguage
                    )
                }
                
                if let mediaURL = self.selectedVideo {
                    SubtitleCacheManager.shared.cacheSubtitles(editableSegments, for: mediaURL)
                }
                
                // print("已使用备用方法加载 \(self.editableSegments.count) 个字幕段落")
            }
        } catch {
            print("备用JSON解析也失败: \(error)")
            print("详细错误信息: \(error.localizedDescription)")
        }
    }

    func saveState() {
        let currentState = EditHistory.State(
            segments: editableSegments,
            selectedId: selectedSegmentId
        )
        editHistory.push(currentState)
    }
    
    // func undo() {
    //     guard let previousState = editHistory.undo() else { return }
    //     editableSegments = previousState.segments
    //     selectedSegmentId = previousState.selectedId
    // }
    
    // func redo() {
    //     guard let nextState = editHistory.redo() else { return }
    //     editableSegments = nextState.segments
    //     selectedSegmentId = nextState.selectedId
    // }

    func exportToSRT() {
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [UTType(filenameExtension: "srt")!]
        savePanel.nameFieldStringValue = "subtitle.srt"
        
        savePanel.begin { result in
            if result == .OK, let url = savePanel.url {
                let srtContent = generateSRTContent()
                do {
                    try srtContent.write(to: url, atomically: true, encoding: .utf8)
                    // print("保存 SRT 文件失败: \(error)")
                } catch {
                    print("保存 SRT 文件失败: \(error)")
                }
            }
        }
    }
    
    func generateSRTContent() -> String {
        var srtContent = ""
        for (index, segment) in editableSegments.enumerated() {
            srtContent += "\(index + 1)\n"
            srtContent += "\(formatTimeForSRT(seconds: segment.startTime)) --> \(formatTimeForSRT(seconds: segment.endTime))\n"
            
            // 处理原始文本，确保单词之间只有一个空格，并去除首尾空格
            let originalText = segment.text
                .components(separatedBy: .whitespaces)
                .filter { !$0.isEmpty }
                .joined(separator: " ")
                .trimmingCharacters(in: .whitespaces) // 去除首尾空格
                srtContent += "\(originalText)\n\n"
            }
        return srtContent
    }
    
    func exportToJSON() {
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [UTType(filenameExtension: "json")!]
        savePanel.nameFieldStringValue = "subtitle.json"
        
        savePanel.begin { result in
            if result == .OK, let url = savePanel.url {
                let jsonContent = generateJSONContent()
                do {
                    let jsonData = try JSONEncoder().encode(jsonContent)
                    try jsonData.write(to: url)
                } catch {
                    print("保存 JSON 文件失败: \(error)")
                }
            }
        }
    }
    
    func generateJSONContent() -> WhisperTranscription {
        // 将编辑后的段落转换回 WhisperTranscription 格式
        let segments = editableSegments.map { segment in
            WhisperTranscription.Segment(
                start: segment.startTime,
                end: segment.endTime,
                text: segment.text,
                words: segment.words.map { word in
                    WhisperTranscription.Word(
                        word: word.word,
                        start: word.start,
                        end: word.end,
                        probability: word.probability
                    )
                }
            )
        }
        
        return WhisperTranscription(
            text: editableSegments.map { $0.text }.joined(separator: " "),
            segments: segments,
            language: transcriptionLanguage // 使用用户选择的语言
        )
    }

    // 添加辅助函数
    func timeStringToSeconds(_ timeString: String) -> Double? {
        let components = timeString.split(separator: ":").map { String($0) }
        guard components.count == 3,
              let hours = Double(components[0]),
              let minutes = Double(components[1]),
              let seconds = Double(components[2]) else {
            return nil
        }
        return hours * 3600 + minutes * 60 + seconds
    }

    func secondsToTimeString(_ seconds: Double) -> String {
        let hours = Int(seconds) / 3600
        let minutes = (Int(seconds) % 3600) / 60
        let secs = Int(seconds) % 60
        return String(format: "%02d:%02d:%02d", hours, minutes, secs)
    }

    // 带参数的导出视频片段函数（用于AI优化界面）
    func exportClip(startTime: String, endTime: String) {
        guard let url = selectedVideo else { return }

        let startSeconds = timeStringToSeconds(startTime) ?? 0
        let endSeconds = timeStringToSeconds(endTime) ?? 0

        exportVideoClipWithTimes(url: url, startTime: startSeconds, endTime: endSeconds)
    }

    func exportVideoClip() {
        guard let url = selectedVideo else { return }

        let startTime = timeStringToSeconds(clipStartTime) ?? 0
        let endTime = timeStringToSeconds(clipEndTime) ?? 0

        exportVideoClipWithTimes(url: url, startTime: startTime, endTime: endTime)
    }

    // 实际的导出逻辑
    private func exportVideoClipWithTimes(url: URL, startTime: Double, endTime: Double) {
        
        let asset = AVAsset(url: url)
        
        // 根据媒体类型选择不同的预设
        let preset = mediaType == .video ? AVAssetExportPresetHighestQuality : AVAssetExportPresetAppleM4A
        guard let exportSession = AVAssetExportSession(asset: asset, presetName: preset) else { return }
        
        // 创建保存面板
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [UTType.mpeg4Movie, UTType.audio] // 添加音频类型
        
        // 设置默认文件名（带时间戳）
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd_HHmmss"
        let timestamp = dateFormatter.string(from: Date())
        
        // 设置默认文件名
        if mediaType == .video {
            savePanel.nameFieldStringValue = "clip_\(timestamp).mp4" 
        } else if mediaType == .audio {
            savePanel.nameFieldStringValue = "clip_\(timestamp).m4a" // 音频就是导出mp3
        }
        
        // 显示保存面板
        // print("显示保存面板")
        savePanel.begin { result in
            if result == .OK, let outputURL = savePanel.url {
                // 配置导出会话
                exportSession.outputURL = outputURL
                if mediaType == .video {
                    exportSession.outputFileType = .mp4 
                } else if mediaType == .audio {
                    exportSession.outputFileType = .m4a // 音频就是导出mp3
                }
                exportSession.timeRange = CMTimeRange(
                    start: CMTime(seconds: startTime, preferredTimescale: 600),
                    duration: CMTime(seconds: endTime - startTime, preferredTimescale: 600)
                )
                
                // 开始导出
                exportSession.exportAsynchronously {
                    DispatchQueue.main.async {
                        switch exportSession.status {
                        case .completed:
                             print("音视频片段已成功导出到: \(outputURL.path)")
                        case .failed:
                            if let error = exportSession.error {
                                 print("音视频导出失败: \(error.localizedDescription)")
                            }
                        case .cancelled:
                             print("音视频导出被取消")
                        default:
                            break
                        }
                    }
                }
            }
        }
    }

    // 更新播放器时间
    func updatePlayerTime(from timeString: String) {
        if let time = timeStringToSeconds(timeString) {
            // 根据当前选中的标签页选择正确的播放器
            switch selectedSegment {
            case 0: // AI优化模块
                aiPlayerManager.seek(to: time)
            case 1: // 文本检测模块
                textDetectionPlayerManager.seek(to: time)
            case 2: // 硬字幕提取模块
                hardSubtitlePlayerManager.seek(to: time)
            default:
                aiPlayerManager.seek(to: time)
            }
        }
    }

    // 分割段落逻辑
    func splitSegmentAtIndex(_ wordIndex: Int, in segmentIndex: Int) {
        guard segmentIndex >= 0 && segmentIndex < editableSegments.count else { return }
        
        let segment = editableSegments[segmentIndex]
        let words = segment.words
        
        // 确保索引在有效范围内
        guard wordIndex > 0 && wordIndex < words.count else { return } // 确保 wordIndex 大于 0
        
        // 找到光标位置对应的单词索引
        let splitWords = Array(words[0..<wordIndex])  // 包含当前单词前的所有单词
        let remainingWords = Array(words[wordIndex...])  // 包含当前单词及其后面的所有单词
        
        // 创建新的段落
        let newSegment = EditableSegment(words: remainingWords)
        
        // 更新段落列表
        editableSegments[segmentIndex] = EditableSegment(words: splitWords)  // 更新当前段落
        editableSegments.insert(newSegment, at: segmentIndex + 1)  // 插入新段落
        
        // 更新选中状态
        selectedSegmentId = newSegment.id  // 选中新的段落
    }

    // 合并段落的逻辑
    func mergeSegments(at index: Int) {
        guard index >= 0 && index < editableSegments.count - 1 else { return }
        
        let currentSegment = editableSegments[index]
        let nextSegment = editableSegments[index + 1]
        
        // 合并两个段落的单词
        let mergedWords = currentSegment.words + nextSegment.words
        let mergedSegment = EditableSegment(words: mergedWords)
        
        // 更新段落列表
        editableSegments[index] = mergedSegment
        editableSegments.remove(at: index + 1)  // 移除下一个段落
        
        // 更新选中状态
        selectedSegmentId = mergedSegment.id  // 选中合并后的段落
    }



    // 添加过滤逻辑
    var filteredSegments: [TextSegment] {
        if searchKeyword.isEmpty {
            return videoProcessor.textSegments
        }
        return videoProcessor.textSegments.filter { segment in
            segment.text.localizedCaseInsensitiveContains(searchKeyword)
        }
    }

    func exportLongImage() {
        let selectedSegments = videoProcessor.textSegments.filter { selectedFrames.contains($0.id) }
        guard !selectedSegments.isEmpty else { return }
        
        // 获取第一个段落的完整帧
        guard let firstSegment = selectedSegments.first,
              let firstFrame = firstSegment.frames.first else { return }
        
        let width = firstFrame.image.size.width
        let firstFrameHeight = firstFrame.image.size.height
        
        // 计算总高度：第一帧高度 + 后续帧文本区域高度（无额外padding）
        var totalHeight = firstFrameHeight
        for segment in selectedSegments.dropFirst() {
            if let frame = segment.frames.first {
                totalHeight += frame.textBounds.height
            }
        }
        
        // 创建长条形图片
        let finalImage = NSImage(size: NSSize(width: width, height: totalHeight))
        finalImage.lockFocus()
        
        // 从顶部开始绘制第一帧（完整显示）
        firstFrame.image.draw(in: NSRect(
            x: 0,
            y: totalHeight - firstFrameHeight,
            width: width,
            height: firstFrameHeight
        ))
        
        // 当前绘制位置
        var currentY = totalHeight - firstFrameHeight
        
        // 绘制后续帧（只显示文本区域，无缝拼接）
        for segment in selectedSegments.dropFirst() {
            guard let frame = segment.frames.first else { continue }
            
            let textRect = frame.textBounds
            let textAreaHeight = textRect.height
            
            // 计算源图片中文本区域的位置（无padding）
            let sourceRect = NSRect(
                x: 0,
                y: frame.image.size.height - textRect.maxY,
                width: width,
                height: textAreaHeight
            )
            
            // 无缝拼接到上一帧
            currentY -= textAreaHeight
            frame.image.draw(
                in: NSRect(
                    x: 0,
                    y: currentY,
                    width: width,
                    height: textAreaHeight
                ),
                from: sourceRect,
                operation: .sourceOver,
                fraction: 1.0
            )
        }
        
        finalImage.unlockFocus()
        
        // 保存长图
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [UTType.png]
        savePanel.nameFieldStringValue = "exported_image.png"
        
        if savePanel.runModal() == .OK {
            if let url = savePanel.url,
               let imageData = finalImage.tiffRepresentation,
               let bitmapImage = NSBitmapImageRep(data: imageData),
               let pngData = bitmapImage.representation(using: .png, properties: [:]) {
                do {
                    try pngData.write(to: url)
                    // print("长图导出到: \(url.path)")
                    
                    // 导成功后，在主线程清除选中状态
                    DispatchQueue.main.async {
                        selectedFrames.removeAll() // 清除所有选中的卡片
                    }
                } catch {
                    print("导出长图失败: \(error.localizedDescription)")
                }
            }
        } else {
            // 用户取消导出时也清除选中状态
            DispatchQueue.main.async {
                selectedFrames.removeAll()
            }
        }
    }

    // 添加新的方法

    func selectMedia() {
        let panel = NSOpenPanel()
        panel.allowedContentTypes = [UTType.movie, UTType.audio]
        panel.allowsMultipleSelection = false
        
        if panel.runModal() == .OK {
            guard let selectedURL = panel.url else { return }
            selectedVideo = selectedURL
            
            // 加载缓存前先清空现有段落（重新转录新视频）
            editableSegments = []
            optimizedSegments = []
            
            // 尝试加载所有缓存
            loadAllCache()
            
            // 判断媒体类型
            let typeIdentifier = try? selectedURL.resourceValues(forKeys: [.typeIdentifierKey]).typeIdentifier
            if let identifier = typeIdentifier {
                if UTType(identifier)?.conforms(to: .audio) ?? false {
                    mediaType = .audio
                } else {
                    mediaType = .video
                }
            }
            
            setupPlayer()
        }
    }

    func setupPlayer() {
        guard let url = selectedVideo else {
            print("错误：未选择视频文件")
            return
        }
        // 为所有三个模块设置独立的播放器
        aiPlayerManager.setupPlayer(with: url)
        textDetectionPlayerManager.setupPlayer(with: url)
        hardSubtitlePlayerManager.setupPlayer(with: url)
    }

    

    func getCurrentSegment() -> EditableSegment? {
        return editableSegments.first { segment in
            currentTime >= segment.startTime && currentTime <= segment.endTime
        }
    }

    // 添加新的辅助函数
    private func toggleSelection(_ id: UUID) {
        if selectedTranslationSegments.contains(id) {
            selectedTranslationSegments.remove(id)
        } else {
            selectedTranslationSegments.insert(id)
        }
    }

    private func deleteSelectedTranslations() {
        // 保存当前状态用于撤销
        saveState()
        
        // 删除选中的段落
        editableSegments.removeAll { segment in
            selectedTranslationSegments.contains(segment.id)
        }
        
        // 清空选择
        selectedTranslationSegments.removeAll()
    }

    // 添加过滤翻译结果的计算属性
    private var filteredTranslationSegments: [EditableSegment] {
        if translationSearchKeyword.isEmpty {
            return editableSegments
        }
        return editableSegments.filter { segment in
            segment.text.localizedCaseInsensitiveContains(translationSearchKeyword) ||
            (segment.translatedText?.localizedCaseInsensitiveContains(translationSearchKeyword) ?? false)
        }
    }

    // 在 ContentView 中添加加载缓存的方法
    func loadAllCache() {
        guard let mediaURL = selectedVideo else { return }
        
        // 加载原始字幕缓存
        if let cachedSegments = SubtitleCacheManager.shared.getCachedSubtitles(for: mediaURL) {
            editableSegments = cachedSegments
            // print("已加载原始字幕缓存")
        }
        
        // 加载AI优化字幕缓存
        let aiCacheKey = mediaURL.appendingPathExtension("ai").path
        let aiURL = URL(fileURLWithPath: aiCacheKey)
        if let cachedAISegments = SubtitleCacheManager.shared.getCachedSubtitles(for: aiURL) {
            optimizedSegments = cachedAISegments
            // print("已加载 AI 优化字幕缓存")
        }
    }

    // 在 ContentView 中添加更新缓存的方法
    private func updateCache() {
        guard let mediaURL = selectedVideo else { return }
        SubtitleCacheManager.shared.cacheSubtitles(editableSegments, for: mediaURL)
    }

    // 添加定时器调度方法
    private func scheduleCacheUpdate() {
        // 取消之前的定时器
        cacheUpdateTimer?.invalidate()
        
        // 设置新的定时器，延迟60秒更新缓存
        cacheUpdateTimer = Timer.scheduledTimer(withTimeInterval: 60.0, repeats: false) { _ in
            CacheManager.shared.saveAllCache()
        }
    }

    // 添加保存所有缓存的方法
    func saveAllCache() {
        // print("开始保存所有缓存数据")
        guard let mediaURL = selectedVideo else { return }
        
        // 保存原始字幕
        if !editableSegments.isEmpty {
            SubtitleCacheManager.shared.cacheSubtitles(editableSegments, for: mediaURL)
            // print("已保存原始字幕缓存")
        }
        
        // 保存优化后的字幕
        if !optimizedSegments.isEmpty {
            // 为 AI 优化页面创建特殊的缓存键
            let aiCacheKey = mediaURL.appendingPathExtension("ai").path
            let aiURL = URL(fileURLWithPath: aiCacheKey)
            SubtitleCacheManager.shared.cacheSubtitles(optimizedSegments, for: aiURL)
            // print("已保存 AI 优化字幕缓存")
        }
    }

    // 添加导出带字幕视频的函数
    private func exportVideoWithSubtitles() {
        guard let videoURL = selectedVideo, let player = aiPlayerManager.player else { return }
        
        // 创建保存面板
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [UTType.mpeg4Movie]
        savePanel.nameFieldStringValue = "video_with_subtitles.mp4"
        
        if savePanel.runModal() == .OK {
            guard let outputURL = savePanel.url else { return }
            
            // 显示进度指示器
            isProcessing = true
            
            // 将导出过程移至后台线程
            DispatchQueue.global(qos: .userInitiated).async {
                do {
                    // 准备源资源
                    let asset = AVAsset(url: videoURL)
                    
                    // 创建导出会话
                    let composition = AVMutableComposition()
                    
                    // 添加视频轨道
                    guard let compositionVideoTrack = composition.addMutableTrack(
                        withMediaType: .video, 
                        preferredTrackID: kCMPersistentTrackID_Invalid
                    ) else {
                        throw NSError(domain: "ExportError", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法创建视频轨道"])
                    }
                    
                    // 添加音频轨道
                    let compositionAudioTrack = composition.addMutableTrack(
                        withMediaType: .audio, 
                        preferredTrackID: kCMPersistentTrackID_Invalid
                    )
                    
                    // 获取源视频轨道
                    guard let videoTrack = asset.tracks(withMediaType: .video).first else {
                        throw NSError(domain: "ExportError", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法获取源视频轨道"])
                    }
                    
                    // 获取源音频轨道（如果有）
                    let audioTracks = asset.tracks(withMediaType: .audio)
                    
                    // 计算视频时长
                    let timeRange = CMTimeRange(start: .zero, duration: asset.duration)
                    
                    // 复制视频轨道
                    try compositionVideoTrack.insertTimeRange(timeRange, of: videoTrack, at: .zero)
                    
                    // 复制音频轨道（如果有）
                    if let audioTrack = audioTracks.first, let compositionAudioTrack = compositionAudioTrack {
                        try compositionAudioTrack.insertTimeRange(timeRange, of: audioTrack, at: .zero)
                    }
                    
                    // 准备字幕合成层
                    let videoSize = videoTrack.naturalSize
                    let layerInstruction = AVMutableVideoCompositionLayerInstruction(assetTrack: compositionVideoTrack)
                    
                    // 确保视频方向正确
                    var transform = videoTrack.preferredTransform
                    layerInstruction.setTransform(transform, at: .zero)
                    
                    // 设置合成指令
                    let instruction = AVMutableVideoCompositionInstruction()
                    instruction.timeRange = timeRange
                    instruction.layerInstructions = [layerInstruction]
                    
                    // 创建视频合成
                    let videoComposition = AVMutableVideoComposition()
                    videoComposition.renderSize = videoSize
                    videoComposition.frameDuration = CMTime(value: 1, timescale: 30) // 30 FPS
                    videoComposition.instructions = [instruction]
                    
                    // 使用Core Animation添加字幕
                    let parentLayer = CALayer()
                    parentLayer.frame = CGRect(x: 0, y: 0, width: videoSize.width, height: videoSize.height)
                    
                    // 源视频层
                    let videoLayer = CALayer()
                    videoLayer.frame = CGRect(x: 0, y: 0, width: videoSize.width, height: videoSize.height)
                    parentLayer.addSublayer(videoLayer)
                    
                    // 字幕层
                    let subtitleLayer = CATextLayer()
                    subtitleLayer.frame = CGRect(
                        x: 0,
                        y: videoSize.height * 0.85, // 位于底部15%位置
                        width: videoSize.width,
                        height: videoSize.height * 0.15
                    )
                    subtitleLayer.alignmentMode = .center
                    subtitleLayer.fontSize = videoSize.width / 30 // 根据视频宽度调整字体大小
                    subtitleLayer.isWrapped = true
                    
                    // 添加字幕背景层以增强可读性
                    let subtitleBackgroundLayer = CALayer()
                    subtitleBackgroundLayer.frame = subtitleLayer.frame
                    subtitleBackgroundLayer.backgroundColor = CGColor(gray: 0, alpha: 0.5)
                    
                    parentLayer.addSublayer(subtitleBackgroundLayer)
                    parentLayer.addSublayer(subtitleLayer)
                    
                    // 设置动画工具
                    videoComposition.animationTool = AVVideoCompositionCoreAnimationTool(
                        postProcessingAsVideoLayer: videoLayer,
                        in: parentLayer
                    )
                    
                    // 创建导出会话
                    let exportSession = AVAssetExportSession(
                        asset: composition,
                        presetName: AVAssetExportPresetHighestQuality
                    )
                    
                    exportSession?.outputURL = outputURL
                    exportSession?.outputFileType = .mp4
                    exportSession?.videoComposition = videoComposition
                    exportSession?.shouldOptimizeForNetworkUse = true
                    
                    // 创建显示字幕的时间表
                    var subtitleDisplayTimes: [(String, CMTime, CMTime)] = [] // (文本, 开始时间, 结束时间)
                    
                    // 根据当前字幕模式选择要添加的字幕
                    for segment in editableSegments {
                        let startTime = CMTime(seconds: segment.startTime, preferredTimescale: 600)
                        let endTime = CMTime(seconds: segment.endTime, preferredTimescale: 600)
                        
                        var subtitleText = ""
                        switch subtitleMode {
                        case .bilingual:
                            if let translatedText = segment.translatedText {
                                subtitleText = "\(translatedText)\n\(segment.text)"
                            } else {
                                subtitleText = segment.text
                            }
                        case .original:
                            subtitleText = segment.text
                        case .translation:
                            if let translatedText = segment.translatedText {
                                subtitleText = translatedText
                            }
                        case .none:
                            subtitleText = ""
                        }
                        
                        subtitleDisplayTimes.append((subtitleText, startTime, endTime))
                    }
                    
                    // 开始导出过程
                    // 首先创建一个空的字幕层
                    subtitleLayer.string = ""
                    
                    // 使用定时器在适当的时间更新字幕内容
                    let completionHandler = exportSession?.exportAsynchronously {
                        DispatchQueue.main.async {
                            isProcessing = false
                            
                            if let error = exportSession?.error {
                                print("导出视频失败: \(error.localizedDescription)")
                            } else {
                                print("带字幕的视频已导出至: \(outputURL.path)")
                            }
                        }
                    }
                    
                    // 循环更新进度和字幕
                    var didComplete = false
                    while !didComplete {
                        // 更新UI进度 - 使用async/await而不是直接调用
                        let progress = exportSession?.progress ?? 0
                        Task { @MainActor in
                            self.progress = Double(progress)
                        }
                        
                        // 计算当前时间
                        let currentExportTime = CMTimeMultiplyByFloat64(
                            asset.duration,
                            multiplier: Float64(progress)
                        )
                        
                        // 更新字幕
                        for (text, startTime, endTime) in subtitleDisplayTimes {
                            if CMTimeCompare(currentExportTime, startTime) >= 0 && 
                               CMTimeCompare(currentExportTime, endTime) <= 0 {
                                // 确保在主线程上更新UI并使用CATransaction防止布局循环
                                DispatchQueue.main.async {
                                    CATransaction.begin()
                                    CATransaction.setDisableActions(true) // 禁用隐式动画
                                    subtitleLayer.string = text
                                    subtitleLayer.foregroundColor = CGColor(red: 1, green: 1, blue: 1, alpha: 1)
                                    CATransaction.commit()
                                }
                                break
                            } else {
                                DispatchQueue.main.async {
                                    CATransaction.begin()
                                    CATransaction.setDisableActions(true)
                                    subtitleLayer.string = ""
                                    CATransaction.commit()
                                }
                            }
                        }
                        
                        // 检查是否已完成
                        didComplete = exportSession?.status != .exporting
                        
                        // 短暂休眠以节省CPU
                        if !didComplete {
                            Thread.sleep(forTimeInterval: 0.1)
                        }
                    }
                } catch {
                    DispatchQueue.main.async {
                        isProcessing = false
                        progress = 0.0
                        // print("导出视频时出错: \(error.localizedDescription)")
                    }
                }
            }
        }
    }

    func createOrUpdateSubtitleOverlay(text: String) {
        // 使用主线程执行UI更新
        DispatchQueue.main.async { [self] in
            // 检查 player 是否有效
            guard let player = self.player else { return }
            
            // 如果字幕叠加层不存在，创建一个
            if self.subtitleOverlay == nil {
                let textField = NSTextField()
                textField.isEditable = false
                textField.isBordered = false
                textField.backgroundColor = NSColor.clear
                textField.textColor = NSColor.white
                textField.font = NSFont.systemFont(ofSize: 20, weight: .medium)
                textField.alignment = .center
                textField.maximumNumberOfLines = 0
                textField.lineBreakMode = .byWordWrapping
                
                // 添加阴影效果使字幕更清晰
                textField.shadow = NSShadow()
                textField.shadow?.shadowColor = NSColor.black
                textField.shadow?.shadowBlurRadius = 2
                textField.shadow?.shadowOffset = NSSize(width: 1, height: 1)
                
                // 确保在主线程上添加子视图
                if Thread.isMainThread {
                    // 获取 VideoPlayer 的视图
                    if let videoPlayerView = NSApp.windows.first?.contentView?.subviews.first(where: { $0 is AVPlayerView }) {
                        videoPlayerView.addSubview(textField)
                        self.subtitleOverlay = textField
                    }
                }
            }
            
            // 更新字幕文本和位置
            if let textField = self.subtitleOverlay,
               let videoPlayerView = textField.superview {  // 确保文本字段仍然附加到视图层次结构中
                CATransaction.begin()
                CATransaction.setDisableActions(true)
                textField.stringValue = text
                textField.sizeToFit()
                
                let x = (videoPlayerView.bounds.width - textField.bounds.width) / 2
                let y = videoPlayerView.bounds.height * 0.15
                textField.frame.origin = NSPoint(x: x, y: y)
                CATransaction.commit()
                
                // 确保字幕视图始终在最前面
                if Thread.isMainThread {
                    videoPlayerView.addSubview(textField)
                }
            }
        }
    }

    // 添加字幕提取方法
    func extractSubtitles() async {
        guard let url = selectedVideo, videoProcessor.subtitleRegion.isSelected else { return }
        
        isExtractingSubtitles = true
        subtitleExtractionProgress = 0
        
        do {
            let segments = try await videoProcessor.processSubtitleRegion(
                url: url,
                region: videoProcessor.subtitleRegion.rect,
                recognitionLevel: recognitionLevel,
                progressHandler: { progress in
                    // 在主线程更新进度
                    DispatchQueue.main.async {
                        subtitleExtractionProgress = progress
                    }
                }
            )
            
            // 处理完成后，将结果转换为SRT格式并导出
            await MainActor.run {
                let srtContent = videoProcessor.exportSubtitlesToSRT()
                self.srtContent = srtContent
                
                // 将提取的字幕段落添加到可编辑段落中
                self.editableSegments = segments
                
                // 自动跳转到转录结果界面
                selectedSegment = 1
                isExtractingSubtitles = false
                
                // 保存字幕缓存
                // if let mediaURL = selectedVideo {
                //     SubtitleCacheManager.shared.cacheSubtitles(segments, for: mediaURL)
                // }
                
                // 显示导出对话框
                exportExtractedSubtitles()
            }
        } catch {
            // print("提取字幕错误: \(error)")
            await MainActor.run {
                isExtractingSubtitles = false
            }
        }
    }
    
    // 新增：高效硬字幕提取方法
    func extractHardSubtitlesEfficiently() async {
        guard let url = selectedVideo else { return }
        
        // 新增：文件大小限制检查（5GB）
        if let fileSize = try? FileManager.default.attributesOfItem(atPath: url.path)[.size] as? NSNumber {
            let maxSize: Int64 = 5 * 1024 * 1024 * 1024 // 5GB
            if fileSize.int64Value > maxSize {
                let alert = NSAlert()
                alert.messageText = "文件过大"
                alert.informativeText = "请选择小于5GB的视频文件进行硬字幕提取。"
                alert.addButton(withTitle: "确定")
                alert.runModal()
                return
            }
        }
        
        isExtractingSubtitles = true
        subtitleExtractionProgress = 0
        
        // 调用新的分块处理方法
        videoProcessor.extractHardSubtitlesBlockwise(
            url: url,
            blockDuration: 120.0, // 2分钟一个块
            frameRate: 2.0, // 每秒2帧
            region: videoProcessor.subtitleRegion.isSelected ? videoProcessor.subtitleRegion.rect : nil, // 传用户选择区域
            recognitionLevel: recognitionLevel,
            progressHandler: { progress in
                // 在主线程更新进度
                DispatchQueue.main.async {
                    subtitleExtractionProgress = progress
                }
            },
            completion: { segments in
                // 在主线程处理结果
                DispatchQueue.main.async {
                    self.editableSegments = segments
                    self.optimizedSegments = segments
                    
                    // 自动跳转到转录结果界面
                    self.selectedSegment = 1
                    self.isExtractingSubtitles = false
                    
                    // 保存字幕缓存
                    if let mediaURL = self.selectedVideo {
                        SubtitleCacheManager.shared.cacheSubtitles(segments, for: mediaURL)
                    }
                    
                    // 显示导出对话框
                    self.exportExtractedSubtitles()
                }
            }
        )
    }

    // 导出提取的字幕
    func exportExtractedSubtitles() {
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [UTType(filenameExtension: "srt")!]
        savePanel.nameFieldStringValue = "extracted_subtitles.srt"
        
        savePanel.begin { result in
            if result == .OK, let url = savePanel.url {
                do {
                    try self.srtContent.write(to: url, atomically: true, encoding: .utf8)
                    // print("字幕已保存到: \(url.path)")
                } catch {
                    print("保存字幕文件失败: \(error)")
                }
            }
        }
    }



    // 添加模型选择器视图
    // var modelSelectorView: some View {
    //     VStack {
    //         Picker("转录模型", selection: $selectedModel) {
    //             Group {
    //                 Text("tiny.en").tag("tiny.en")
    //                 Text("tiny").tag("tiny")
    //                 Text("small.en").tag("small.en")
    //                 Text("small").tag("small")
                    
    //                 // 专业模型添加禁用逻辑
    //                 Text("large-v2(949MB) 🔒").tag("large-v2_949MB")
    //                     .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
    //                 Text("large-v2_turbo(955MB) 🔒").tag("large-v2_turbo_955MB")
    //                     .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
    //                 Text("large-v3 🔒").tag("large-v3")
    //                     .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
    //                 Text("large-v3_turbo 🔒").tag("large-v3_turbo")
    //                     .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
    //                 Text("large-v3-v20240930 🔒").tag("large-v3-v20240930")
    //                     .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
    //                 Text("large-v3-v20240930_turbo 🔒").tag("large-v3-v20240930_turbo")
    //                     .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
    //             }
    //         }
    //         .pickerStyle(MenuPickerStyle())
    //         .onChange(of: selectedModel) { newModel in
    //             // 如果选择了专业模型但没有许可证，显示提示并回退到免费模型
    //             if isProModel(newModel) && !LicenseManager.shared.isPro() { // 替换为 isPro
    //                 showProFeatureAlert() // 不再传递参数
    //                 // 回退到默认免费模型
    //                 DispatchQueue.main.async {
    //                     selectedModel = "small"
    //                 }
    //             }
    //         }
    //     }
    // }



    // 在段落文本编辑完成后触发缓存
    func onSegmentEdit() {
        scheduleCacheUpdate()
    }

    // 在翻译文本编辑完成后触发缓存
    func onTranslationEdit() {
        scheduleCacheUpdate()
    }

    // 片段重新转录功能
    func reTranscribeSegment(_ segment: EditableSegment) {
        let start = segment.startTime
        let end = segment.endTime
        guard let url = selectedVideo else { return }
        let asset = AVAsset(url: url)
        let preset = mediaType == .video ? AVAssetExportPresetAppleM4A : AVAssetExportPresetAppleM4A
        guard let exportSession = AVAssetExportSession(asset: asset, presetName: preset) else { return }
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("reclip_\(UUID().uuidString).m4a")
        exportSession.outputURL = tempURL
        exportSession.outputFileType = .m4a
        exportSession.timeRange = CMTimeRange(start: CMTime(seconds: start, preferredTimescale: 600), duration: CMTime(seconds: end - start, preferredTimescale: 600))
        exportSession.exportAsynchronously {
            DispatchQueue.main.async {
                if exportSession.status == .completed {
                    self.transcribeAudioForURL(url: tempURL, offset: start) { newSegments in
                        // 替换原有 segment
                        // if let idx = self.editableSegments.firstIndex(where: { $0.id == segment.id }) {
                        //     self.editableSegments.remove(at: idx)
                        //     self.editableSegments.insert(contentsOf: newSegments, at: idx)
                        // }
                        if let idx = self.optimizedSegments.firstIndex(where: { $0.id == segment.id }) {
                            self.optimizedSegments.remove(at: idx)
                            self.optimizedSegments.insert(contentsOf: newSegments, at: idx)
                        }
                        // 清理临时文件
                        try? FileManager.default.removeItem(at: tempURL)
                    }
                } else {
                    print("片段导出失败: \(exportSession.error?.localizedDescription ?? "未知错误")")
                }
            }
        }
    }

    // 针对片段的异步转录（不影响全局 editableSegments）
    func transcribeAudioForURL(url: URL, offset: Double, completion: @escaping ([EditableSegment]) -> Void) {
        Task {
            do {
                let whisperKit = try await ensureWhisperModel(modelName: selectedModel)
                let options = DecodingOptions(
                    verbose: true,
                    task: .transcribe,
                    language: transcriptionLanguage,
                    skipSpecialTokens: true,
                    withoutTimestamps: false,
                    wordTimestamps: true
                )
                let results = try await whisperKit.transcribe(audioPath: url.path, decodeOptions: options)
                guard let result = results.first else { completion([]); return }
                // 写入临时 JSON
                let saveResult = writeJSONFile(result: result)
                switch saveResult {
                case .success(let jsonURL):
                    let jsonData = try Data(contentsOf: jsonURL)
                    let transcription = try JSONDecoder().decode(WhisperTranscription.self, from: jsonData)
                    let isCJKLanguage = ["ja", "zh", "zh-Hans", "zh-Hant", "ko","th"].contains(transcriptionLanguage)

                    // 🔑 简单判断：有词级时间戳就用词级，没有就用段落级
                    let segments: [EditableSegment] = transcription.segments.map { seg in
                        if !seg.words.isEmpty {
                            // 有词级时间戳：使用词级处理
                            let words = seg.words.map { w in
                                EditableWord(word: cleanText(w.word), start: w.start + offset, end: w.end + offset, probability: w.probability)
                            }
                            return EditableSegment(words: words, isCJKLanguage: isCJKLanguage)
                        } else {
                            // 没有词级时间戳：使用段落级处理
                            let word = EditableWord(word: cleanText(seg.text), start: seg.start + offset, end: seg.end + offset, probability: 1.0)
                            return EditableSegment(words: [word], isCJKLanguage: isCJKLanguage)
                        }
                    }
                    completion(segments)
                    try? FileManager.default.removeItem(at: jsonURL)
                case .failure(let error):
                    print("写入JSON失败: \(error.localizedDescription)")
                    completion([])
                }
            } catch {
                print("片段转录失败: \(error.localizedDescription)")
                completion([])
            }
        }
    }
}

struct EditableWord: Identifiable {
    var id = UUID()
    let word: String
    let start: Double
    let end: Double
    let probability: Double
}

struct CorrectionSuggestion: Codable, Hashable, Identifiable {
    let id = UUID()
    let originalText: String
    let correctedText: String
    let category: String

    enum CodingKeys: String, CodingKey {
        case originalText = "original_text"
        case correctedText = "corrected_text"
        case category
    }
}

struct EditableSegment: Identifiable {
    var id: UUID
    var words: [EditableWord]
    let isCJKLanguage: Bool  // 添加标识是否为中文文本
    var correctionSuggestions: [CorrectionSuggestion]?
    
    var text: String {
        if isCJKLanguage {
            // 中文不需要空格分隔
            return words.map { $0.word }.joined()
        } else {
            // 其他语言需要空格分隔
            return words.map { $0.word }.joined(separator: " ")
        }
    }
    
    var startTime: Double {
        words.first?.start ?? 0
    }
    
    var endTime: Double {
        words.last?.end ?? 0
    }

    var translatedText: String? // 新增属性用存储翻译结果
    
    // 添加初始化方法
    init(id: UUID = UUID(), words: [EditableWord], isCJKLanguage: Bool = false) {
        self.id = id
        self.words = words
        self.isCJKLanguage = isCJKLanguage
    }
    
    // 添加更新文本的方法
    mutating func updateText(_ newText: String) {
        if isCJKLanguage {
            // 中文文本作为整体存储在一个 EditableWord 中
            // 对于中文、日语等语言，不进行分词处理，保持整段文本
            words = [EditableWord(
                word: newText,
                start: words.first?.start ?? 0,
                end: words.last?.end ?? 0,
                probability: 1.0
            )]
        } else {
            // 其他语言按空格分割
            let newWords = newText.split(separator: " ").map { word -> EditableWord in
                EditableWord(
                    word: String(word),
                    start: words.first?.start ?? 0,
                    end: words.last?.end ?? 0,
                    probability: 1.0
                )
            }
            words = newWords
        }
    }
}

struct SegmentView: View {  
    let segment: EditableSegment
    let isSelected: Bool
    let isLastSegment: Bool
    @Binding var cursorPosition: Int?
    let onSelect: () -> Void
    let onSplit: (Int) -> Void
    let onMerge: () -> Void
    let onTimeClick: (Double) -> Void
    let onUpdateText: (String) -> Void
    let onUpdateTranslation: (String) -> Void
    let onEdit: () -> Void // 新增编辑回调
    let onAcceptSuggestion: (CorrectionSuggestion) -> Void // 新增 接受
    let onIgnoreSuggestion: (CorrectionSuggestion) -> Void // 新增“忽略”回调
    let onReTranscribe: () -> Void // 新增回调

    @State private var isEditingOriginal: Bool = false
    @State private var isEditingTranslation: Bool = false
    @FocusState private var isTextEditorFocused: Bool // 添加焦点状态
    @Environment(\.scenePhase) private var scenePhase // 用于检测点击外部
    @State private var localText: String = "" // 原文本地文本状态
    @State private var localTranslationText: String = "" // 添加翻译文本的本地状态
    
    var body: some View {
        HStack(alignment: .top, spacing: 2) {
            // 左侧内容
            VStack(alignment: .leading, spacing: 6) {
                // 合并按钮
                if !isLastSegment { // 使用 isLastSegment
                    Button("合并") {
                        onMerge()  // 调用合并回调
                    }
                    .frame(width: 50) // 设置按钮宽度
                    .buttonStyle(BorderlessButtonStyle())
                    .foregroundColor(.white) // 设置按钮文本颜色白色
                    .background(Color.blue.opacity(0.3)) // 设置按钮背景颜色
                    .cornerRadius(5) // 设置圆角  
                }
                
                // 编辑按钮
                Button(isEditingOriginal ? "完成" : "编辑") {
                    if !isEditingOriginal {
                        // 进入编辑模式时，初始化本地文本
                        localText = segment.text
                    }
                    isEditingOriginal.toggle()
                }
                .frame(width: 50)
                .buttonStyle(BorderlessButtonStyle())
                .foregroundColor(.white)
                .background(Color.green.opacity(0.3))
                .cornerRadius(5)
            }
            .padding(.top, 50)
            .frame(width: 80)
            
            // 右侧内容
            VStack(alignment: .leading, spacing: 2) {
                // 可点击的时间戳
                // 时间戳和重新转录按钮并排
                HStack {
                    Button(action: {
                        onTimeClick(segment.startTime)
                    }) {
                        Text("\(TimeFormatter.formatSRT(seconds: segment.startTime)) --> \(TimeFormatter.formatSRT(seconds: segment.endTime))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(8)
                            .background(Color(NSColor.controlBackgroundColor).opacity(0.5))
                            .cornerRadius(2)
                    }
                    .buttonStyle(PlainButtonStyle())

                    Button(action: {
                        onReTranscribe()
                    }) {
                        Image(systemName: "arrow.clockwise.circle")
                            .help("重新转录本片段")
                    }
                }
                
                // 原文编辑区域
                if isEditingOriginal {
                    TextEditor(text: $localText)
                        .frame(height: 60)
                        .padding(4)
                        .background(Color(NSColor.textBackgroundColor)) // 设置文本框背景颜色
                        .cornerRadius(4)
                        .onChange(of: localText) { newValue in
                            onUpdateText(newValue)
                            onEdit()
                        }
                } else {
                    // 可点击的段落区域
                    HStack(spacing: 4) {
                        ForEach(segment.words, id: \.id) { word in
                            Text(word.word)
                                .padding(.horizontal, 4)
                                .padding(.vertical, 1)
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(4)
                                .onTapGesture {
                                    onSelect()
                                    let index = segment.words.firstIndex(where: { $0.id == word.id })!
                                    onSplit(index)
                                }
                        }
                    }
                    .padding(8)
                    .background(isSelected ? Color.green.opacity(0.1) : Color.clear) //合并操作后显示绿色背景
                    .cornerRadius(4)
                }

                // ✅ 核心UI逻辑：如果存在校对建议，就显示它们
                if let suggestions = segment.correctionSuggestions, !suggestions.isEmpty {
                    // 使用 ForEach 遍历并显示每一条建议
                    ForEach(suggestions) { suggestion in
                        HStack(spacing: 5) {
                            Image(systemName: "sparkle") // 魔法棒图标
                                .foregroundColor(.orange)
                            Text("AI建议:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("\"\(suggestion.originalText)\"")
                                .strikethrough(color: .red)
                            Image(systemName: "arrow.right")
                            Text("\"\(suggestion.correctedText)\"")
                                .foregroundColor(.green)
                            
                            Spacer()
                            
                            Button {
                                // 点击时，调用回调函数
                                onAcceptSuggestion(suggestion)
                            } label: {
                                Image(systemName: "checkmark.circle.fill")
                                Text("接受")
                            }
                            .buttonStyle(.borderless)
                            .foregroundColor(.accentColor)
                            .controlSize(.small)

                            // ✅ 新增“忽略”按钮
                            Button {
                                onIgnoreSuggestion(suggestion)
                            } label: {
                                Image(systemName: "xmark.circle")
                                Text("忽略")
                            }
                            .buttonStyle(.borderless)
                            .foregroundColor(.gray)
                            .controlSize(.small)
                        }
                        .font(.caption)
                        .padding(6)
                        .background(Color.secondary.opacity(0.1))
                        .cornerRadius(6)
                        .transition(.asymmetric(insertion: .scale, removal: .opacity))
                    }
                }
                
                // 翻译文本编辑区域
                if let translatedText = segment.translatedText {
                    if isEditingTranslation {
                        TextEditor(text: $localTranslationText)
                            .focused($isTextEditorFocused) // 添加焦点绑定
                            .frame(height: 60)
                            .foregroundColor(.gray)
                            .padding(4)
                            .background(Color(NSColor.textBackgroundColor))// 设置背景颜色
                            .cornerRadius(4)
                            .onChange(of: localTranslationText) { newValue in
                                onUpdateTranslation(newValue)
                                onEdit() // 调用编辑回调
                            }
                            .onChange(of: isTextEditorFocused) { isFocused in
                                // 当失去焦点时关闭编辑模式
                                if !isFocused {
                                    isEditingTranslation = false
                                }
                            }
                    } else {
                        Text(translatedText)
                            .foregroundColor(.gray)
                            .padding(8)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color.clear)
                                    .contentShape(Rectangle())
                            )
                            .onTapGesture {
                                localTranslationText = translatedText // 初始化本地翻译文本
                                isEditingTranslation = true
                                isTextEditorFocused = true // 自动获取焦点
                            }
                    }
                }
            }
        }
        .padding(.vertical, 4)
        .padding(.horizontal, 10)
        .background(segment.correctionSuggestions != nil && segment.correctionSuggestions?.isEmpty == false ? Color.yellow.opacity(0.2) : Color.clear)
        .animation(.default, value: segment.correctionSuggestions) // 当建议被移除时，添加动画效果
        // 添加点击外部区域关闭编辑框的功能
        .onTapGesture {
            if isEditingTranslation {
                isEditingTranslation = false
                isTextEditorFocused = false
            }
        }
    }
}

struct EditHistory {
    private(set) var undoStack: [State] = []
    private(set) var redoStack: [State] = []
    
    struct State {
        let segments: [EditableSegment]
        let selectedId: UUID?
    }
    
    mutating func push(_ state: State) {
        undoStack.append(state)
        redoStack.removeAll() // 清除做栈
    }
    
    mutating func undo() -> State? {
        guard let current = undoStack.popLast() else { return nil }
        redoStack.append(current)
        return undoStack.last
    }
    
    mutating func redo() -> State? {
        guard let next = redoStack.popLast() else { return nil }
        undoStack.append(next)
        return next
    }
}

struct SegmentDropDelegate: DropDelegate {
    let item: EditableSegment
    @Binding var items: [EditableSegment]
    @Binding var draggedItem: UUID?
    
    func performDrop(info: DropInfo) -> Bool {
        guard let draggedItem = draggedItem else { return false }
        let fromIndex = items.firstIndex { $0.id == draggedItem }
        let toIndex = items.firstIndex { $0.id == item.id }
        
        if let fromIndex = fromIndex, let toIndex = toIndex {
            withAnimation {
                let item = items.remove(at: fromIndex)
                items.insert(item, at: toIndex)
            }
        }
        
        return true
    }
    
    func dropUpdated(info: DropInfo) -> DropProposal? {
        return DropProposal(operation: .move)
    }
}

// 添加一个时间格式化工具类
struct TimeFormatter {
    static func formatSRT(seconds: Double) -> String {
        let hours = Int(seconds) / 3600
        let minutes = (Int(seconds) % 3600) / 60
        let secs = Int(seconds) % 60
        let milliseconds = Int((seconds - Double(Int(seconds))) * 1000)
        
        return String(format: "%02d:%02d:%02d,%03d", hours, minutes, secs, milliseconds)
    }
    
    static func formatSimple(seconds: Double) -> String {
        let minutes = Int(seconds) / 60
        let secs = Int(seconds) % 60
        return String(format: "%02d:%02d", minutes, secs)
    }
}

// 确保 EditableSegment 和相关类型可以被编码和解码
extension EditableSegment: Codable {
    enum CodingKeys: String, CodingKey {
        case id
        case words
        case translatedText
        case isCJKLanguage
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        words = try container.decode([EditableWord].self, forKey: .words)
        translatedText = try container.decodeIfPresent(String.self, forKey: .translatedText)
        isCJKLanguage = try container.decode(Bool.self, forKey: .isCJKLanguage)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(words, forKey: .words)
        try container.encode(translatedText, forKey: .translatedText)
        try container.encode(isCJKLanguage, forKey: .isCJKLanguage)
    }
}

extension EditableWord: Codable {
    enum CodingKeys: String, CodingKey {
        case id
        case word
        case start
        case end
        case probability
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        word = try container.decode(String.self, forKey: .word)
        start = try container.decode(Double.self, forKey: .start)
        end = try container.decode(Double.self, forKey: .end)
        probability = try container.decode(Double.self, forKey: .probability)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(word, forKey: .word)
        try container.encode(start, forKey: .start)
        try container.encode(end, forKey: .end)
        try container.encode(probability, forKey: .probability)
    }
}

// 添加以下辅助视图
struct TextBoundingBoxesView: View {
    let textSegments: [TextSegment]
    let currentTime: Double
    
    var body: some View {
        GeometryReader { geometry in
            ForEach(textSegments) { segment in
                if isSegmentVisible(segment) {
                    let scaledRect = scaleRect(segment.textBounds, to: geometry.size)
                    Rectangle()
                        .stroke(Color.red, lineWidth: 2)
                        .frame(
                            width: scaledRect.width,
                            height: scaledRect.height
                        )
                        .position(
                            x: scaledRect.midX,
                            y: geometry.size.height - scaledRect.midY // 关键修改：翻转 Y 坐标
                        )
                        .overlay(
                            Text(segment.text)
                                .font(.caption2)
                                .foregroundColor(.red)
                                .padding(2)
                                .background(Color.black.opacity(0.5))
                                .cornerRadius(2)
                                .position(
                                    x: scaledRect.midX,
                                    y: geometry.size.height - scaledRect.maxY - 10 // 调整文本位置
                                )
                        )
                }
            }
        }
    }
    
    private func isSegmentVisible(_ segment: TextSegment) -> Bool {
        return currentTime >= segment.startTime && currentTime <= segment.endTime
    }
    
    private func scaleRect(_ rect: CGRect, to size: CGSize) -> CGRect {
        // Vision 框架返回的坐标是从左下角开始的，需要转换为左上角开始
        let normalizedRect = CGRect(
            x: rect.origin.x,
            y: rect.origin.y,
            width: rect.width,
            height: rect.height
        )
        
        // 缩放到实际尺寸
        return CGRect(
            x: normalizedRect.origin.x * size.width,
            y: normalizedRect.origin.y * size.height,
            width: normalizedRect.width * size.width,
            height: normalizedRect.height * size.height
        )
    }
}

struct TextSegmentView: View {
    let segment: TextSegment
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 4) {
                Text(segment.text)
                    .lineLimit(2)
                
                HStack {
                    Text(String(format: "%.2f", segment.startTime))
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("-")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(String(format: "%.2f", segment.endTime))
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    // 显示置信度
                    Text(String(format: "置信度: %.1f%%", segment.confidence * 100))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(8)
            .background(Color.secondary.opacity(0.1))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 在 ContentView 外部添加 VideoPlayerView 定义
struct VideoPlayerView: NSViewRepresentable {
    let player: AVPlayer
    
    func makeNSView(context: Context) -> AVPlayerView {
        let view = AVPlayerView()
        view.player = player
        view.controlsStyle = .floating
        view.showsFullScreenToggleButton = true
        return view
    }
    
    func updateNSView(_ nsView: AVPlayerView, context: Context) {
        nsView.player = player
    }
}

// 添加新的卡片视图组件
struct TextSegmentCard: View {
    let segment: TextSegment
    let highlightedText: AttributedString // 新增高亮文本参数
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 4) {
                // 缩略图（如果有）
                if let frame = segment.frames.first {
                    Image(nsImage: frame.image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(height: 100)
                }

                // 显示高亮文本
                Text(highlightedText)
                    .lineLimit(2)
                    .font(.caption)
                
                // 时间和置信度
                HStack {
                    Text(String(format: "%.2f", segment.startTime))
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(String(format: "%.0f%%", segment.confidence * 100))
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .padding(8)
            .frame(maxWidth: .infinity)
            .background(Color.secondary.opacity(0.1))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 转录设置对话框视图
struct TranscriptionSettingsView: View {
    @Binding var transcriptionLanguage: String
    @Binding var selectedModel: String
    @Binding var enableVAD: Bool

    let onConfirm: () -> Void
    let onCancel: () -> Void

    var body: some View {
        VStack(spacing: 20) {
            // 标题
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("转录设置")
                        .font(.title2)
                        .fontWeight(.semibold)
                    Spacer()
                }
            }

            // 设置选项
            VStack(alignment: .leading, spacing: 16) {
                // 转录语言选择
                VStack(alignment: .leading, spacing: 8) {
                    Text("转录语言")
                        .font(.headline)

                    Picker("转录语言", selection: $transcriptionLanguage) {
                        Text("英语").tag("en")
                        Text("中文").tag("zh")
                        Text("日语").tag("ja")
                        Text("韩语").tag("ko")
                        Text("法语").tag("fr")
                        Text("德语").tag("de")
                        Text("西班牙语").tag("es")
                        Text("意大利语").tag("it")
                        Text("泰语").tag("th")
                    }
                    .pickerStyle(MenuPickerStyle())
                    .onChange(of: transcriptionLanguage) { newLanguage in
                        if newLanguage != "en" && selectedModel == "small.en" {
                            // 如果选择了中文、日语、韩语但模型是 small.en，自动切换到 small
                            selectedModel = "small"
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }

                // 转录模型选择
                VStack {
                    Picker("转录模型", selection: $selectedModel) {
                        Group {
                            Text("tiny.en").tag("tiny.en")
                            Text("tiny").tag("tiny")
                            Text("small.en").tag("small.en")
                            Text("small").tag("small")
                            
                            // 专业模型添加禁用逻辑
                            Text("large-v2(949MB) 🔒").tag("large-v2_949MB")
                                .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
                            Text("large-v2_turbo(955MB) 🔒").tag("large-v2_turbo_955MB")
                                .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
                            Text("large-v3 🔒").tag("large-v3")
                                .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
                            Text("large-v3_turbo 🔒").tag("large-v3_turbo")
                                .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
                            Text("large-v3-v20240930 🔒").tag("large-v3-v20240930")
                                .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
                            Text("large-v3-v20240930_turbo 🔒").tag("large-v3-v20240930_turbo")
                                .disabled(!LicenseManager.shared.isPro()) // 替换为 isPro
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .onChange(of: selectedModel) { newModel in
                        // 🔑 如果选择了专业模型但没有许可证，显示提示并回退到免费模型
                        if ProFeatureHelper.shared.isProModel(newModel) && !LicenseManager.shared.isPro() {
                            ProFeatureHelper.shared.showProFeatureAlert(closeTranscriptionSettings: onCancel)
                            // 回退到默认免费模型
                            DispatchQueue.main.async {
                                selectedModel = "small"
                            }
                        }
                    }
                }

                // VAD智能分块设置
                VStack(alignment: .leading, spacing: 8) {
                    Text("高级选项")
                        .font(.headline)

                    HStack {
                        Toggle("启用VAD智能分块", isOn: $enableVAD)
                            .toggleStyle(SwitchToggleStyle())

                        Spacer()
                    }

                    Text("音频分段策略：选择 VAD 可在静音处智能分割音频，可提升转录准确率")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.vertical, 8)

            Spacer()

            // 操作按钮
            HStack(spacing: 12) {
                Button("取消") {
                    onCancel()
                }
                .buttonStyle(.bordered)
                .keyboardShortcut(.escape)
                Spacer()

                Button("开始转录") {
                    // 🔑 @AppStorage会自动保存设置，无需手动保存
                    onConfirm()
                }
                .buttonStyle(.borderedProminent)
                .keyboardShortcut(.return)
            }
        }
        .padding(24)
        .frame(width: 400, height: 350)
    }
}

// MARK: - 全局辅助函数
func highlightText(_ text: String, keyword: String) -> AttributedString {
    guard !keyword.isEmpty else {
        return AttributedString(text)
    }

    var attributedString = AttributedString(text)

    do {
        // 创建正则表达式
        let regex = try NSRegularExpression(
            pattern: NSRegularExpression.escapedPattern(for: keyword),
            options: .caseInsensitive
        )

        // 获取所有匹配
        let matches = regex.matches(
            in: text,
            range: NSRange(location: 0, length: text.count)
        )

        // 处理每个匹配
        for match in matches {
            if let range = Range(match.range, in: text) {
                // 直接在原始的 AttributedString 上设置属性
                let lowerBound = text.distance(from: text.startIndex, to: range.lowerBound)
                let upperBound = text.distance(from: text.startIndex, to: range.upperBound)
                let attributedRange = attributedString.range(of: text[range])

                if let attributedRange = attributedRange {
                    attributedString[attributedRange].backgroundColor = .yellow
                }
            }
        }
    } catch {
        print("正则表达式错误: \(error)")
    }

    return attributedString
}

// MARK: - 字幕区域选择覆盖层
struct SubtitleRegionSelectionOverlay: View {
    @Binding var isSelecting: Bool
    @Binding var selectedRegion: CGRect
    let imageSize: CGSize

    @State private var startPoint: CGPoint?
    @State private var currentPoint: CGPoint?

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                Color.black.opacity(0.5)
                    .edgesIgnoringSafeArea(.all)

                // 绘制选择区域
                if let start = startPoint, let current = currentPoint {
                    let rect = calculateRect(from: start, to: current, in: geometry.size)
                    Rectangle()
                        .stroke(Color.yellow, lineWidth: 2)
                        .background(Color.yellow.opacity(0.2))
                        .frame(width: rect.width, height: rect.height)
                        .position(x: rect.midX, y: rect.midY)
                }

                // 说明文字
                VStack {
                    Text("请拖动鼠标选择字幕区域")
                        .foregroundColor(.white)
                        .padding()
                        .background(Color.black.opacity(0.7))
                        .cornerRadius(8)

                    Spacer()
                }
                .padding(.top, 20)
            }
            .contentShape(Rectangle())
            .gesture(
                DragGesture(minimumDistance: 0)
                    .onChanged { value in
                        if startPoint == nil {
                            startPoint = value.startLocation
                        }
                        currentPoint = value.location
                    }
                    .onEnded { value in
                        if let start = startPoint, let current = currentPoint {
                            let rect = calculateRect(from: start, to: current, in: geometry.size)
                            // 转换为标准化坐标（0-1范围）
                            selectedRegion = normalizeRect(rect, in: geometry.size)
                            // 重置状态
                            startPoint = nil
                            currentPoint = nil
                        }
                    }
            )
        }
    }

    // 计算矩形区域
    private func calculateRect(from start: CGPoint, to end: CGPoint, in size: CGSize) -> CGRect {
        let minX = min(start.x, end.x)
        let minY = min(start.y, end.y)
        let width = abs(end.x - start.x)
        let height = abs(end.y - start.y)

        return CGRect(x: minX, y: minY, width: width, height: height)
    }

    // 将像素坐标转换为标准化坐标（0-1范围）
    private func normalizeRect(_ rect: CGRect, in size: CGSize) -> CGRect {
        return CGRect(
            x: rect.origin.x / size.width,
            y: rect.origin.y / size.height,
            width: rect.width / size.width,
            height: rect.height / size.height
        )
    }
}
